// Code generated by protoc-gen-go. DO NOT EDIT.
// source: nacos_grpc_service.proto

package nacos_grpc_service

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import anypb "google.golang.org/protobuf/types/known/anypb"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Metadata struct {
	Type                 string            `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	ClientIp             string            `protobuf:"bytes,8,opt,name=clientIp,proto3" json:"clientIp,omitempty"`
	Headers              map[string]string `protobuf:"bytes,7,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Metadata) Reset()         { *m = Metadata{} }
func (m *Metadata) String() string { return proto.CompactTextString(m) }
func (*Metadata) ProtoMessage()    {}
func (*Metadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_nacos_grpc_service_57ee112b0872b14f, []int{0}
}
func (m *Metadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Metadata.Unmarshal(m, b)
}
func (m *Metadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Metadata.Marshal(b, m, deterministic)
}
func (dst *Metadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Metadata.Merge(dst, src)
}
func (m *Metadata) XXX_Size() int {
	return xxx_messageInfo_Metadata.Size(m)
}
func (m *Metadata) XXX_DiscardUnknown() {
	xxx_messageInfo_Metadata.DiscardUnknown(m)
}

var xxx_messageInfo_Metadata proto.InternalMessageInfo

func (m *Metadata) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *Metadata) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *Metadata) GetHeaders() map[string]string {
	if m != nil {
		return m.Headers
	}
	return nil
}

type Payload struct {
	Metadata             *Metadata  `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Body                 *anypb.Any `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Payload) Reset()         { *m = Payload{} }
func (m *Payload) String() string { return proto.CompactTextString(m) }
func (*Payload) ProtoMessage()    {}
func (*Payload) Descriptor() ([]byte, []int) {
	return fileDescriptor_nacos_grpc_service_57ee112b0872b14f, []int{1}
}
func (m *Payload) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Payload.Unmarshal(m, b)
}
func (m *Payload) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Payload.Marshal(b, m, deterministic)
}
func (dst *Payload) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Payload.Merge(dst, src)
}
func (m *Payload) XXX_Size() int {
	return xxx_messageInfo_Payload.Size(m)
}
func (m *Payload) XXX_DiscardUnknown() {
	xxx_messageInfo_Payload.DiscardUnknown(m)
}

var xxx_messageInfo_Payload proto.InternalMessageInfo

func (m *Payload) GetMetadata() *Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (m *Payload) GetBody() *anypb.Any {
	if m != nil {
		return m.Body
	}
	return nil
}

func init() {
	proto.RegisterType((*Metadata)(nil), "Metadata")
	proto.RegisterMapType((map[string]string)(nil), "Metadata.HeadersEntry")
	proto.RegisterType((*Payload)(nil), "Payload")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RequestClient is the client API for Request service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RequestClient interface {
	// Sends a commonRequest
	Request(ctx context.Context, in *Payload, opts ...grpc.CallOption) (*Payload, error)
}

type requestClient struct {
	cc *grpc.ClientConn
}

func NewRequestClient(cc *grpc.ClientConn) RequestClient {
	return &requestClient{cc}
}

func (c *requestClient) Request(ctx context.Context, in *Payload, opts ...grpc.CallOption) (*Payload, error) {
	out := new(Payload)
	err := c.cc.Invoke(ctx, "/Request/request", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RequestServer is the server API for Request service.
type RequestServer interface {
	// Sends a commonRequest
	Request(context.Context, *Payload) (*Payload, error)
}

func RegisterRequestServer(s *grpc.Server, srv RequestServer) {
	s.RegisterService(&_Request_serviceDesc, srv)
}

func _Request_Request_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Payload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RequestServer).Request(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Request/Request",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RequestServer).Request(ctx, req.(*Payload))
	}
	return interceptor(ctx, in, info, handler)
}

var _Request_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Request",
	HandlerType: (*RequestServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "request",
			Handler:    _Request_Request_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "nacos_grpc_service.proto",
}

// BiRequestStreamClient is the client API for BiRequestStream service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BiRequestStreamClient interface {
	// Sends a commonRequest
	RequestBiStream(ctx context.Context, opts ...grpc.CallOption) (BiRequestStream_RequestBiStreamClient, error)
}

type biRequestStreamClient struct {
	cc *grpc.ClientConn
}

func NewBiRequestStreamClient(cc *grpc.ClientConn) BiRequestStreamClient {
	return &biRequestStreamClient{cc}
}

func (c *biRequestStreamClient) RequestBiStream(ctx context.Context, opts ...grpc.CallOption) (BiRequestStream_RequestBiStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &_BiRequestStream_serviceDesc.Streams[0], "/BiRequestStream/requestBiStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &biRequestStreamRequestBiStreamClient{stream}
	return x, nil
}

type BiRequestStream_RequestBiStreamClient interface {
	Send(*Payload) error
	Recv() (*Payload, error)
	grpc.ClientStream
}

type biRequestStreamRequestBiStreamClient struct {
	grpc.ClientStream
}

func (x *biRequestStreamRequestBiStreamClient) Send(m *Payload) error {
	return x.ClientStream.SendMsg(m)
}

func (x *biRequestStreamRequestBiStreamClient) Recv() (*Payload, error) {
	m := new(Payload)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// BiRequestStreamServer is the server API for BiRequestStream service.
type BiRequestStreamServer interface {
	// Sends a commonRequest
	RequestBiStream(BiRequestStream_RequestBiStreamServer) error
}

func RegisterBiRequestStreamServer(s *grpc.Server, srv BiRequestStreamServer) {
	s.RegisterService(&_BiRequestStream_serviceDesc, srv)
}

func _BiRequestStream_RequestBiStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(BiRequestStreamServer).RequestBiStream(&biRequestStreamRequestBiStreamServer{stream})
}

type BiRequestStream_RequestBiStreamServer interface {
	Send(*Payload) error
	Recv() (*Payload, error)
	grpc.ServerStream
}

type biRequestStreamRequestBiStreamServer struct {
	grpc.ServerStream
}

func (x *biRequestStreamRequestBiStreamServer) Send(m *Payload) error {
	return x.ServerStream.SendMsg(m)
}

func (x *biRequestStreamRequestBiStreamServer) Recv() (*Payload, error) {
	m := new(Payload)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _BiRequestStream_serviceDesc = grpc.ServiceDesc{
	ServiceName: "BiRequestStream",
	HandlerType: (*BiRequestStreamServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "requestBiStream",
			Handler:       _BiRequestStream_RequestBiStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "nacos_grpc_service.proto",
}

func init() {
	proto.RegisterFile("nacos_grpc_service.proto", fileDescriptor_nacos_grpc_service_57ee112b0872b14f)
}

var fileDescriptor_nacos_grpc_service_57ee112b0872b14f = []byte{
	// 322 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x51, 0x41, 0x4b, 0xeb, 0x40,
	0x10, 0x7e, 0xdb, 0xf6, 0xbd, 0xa4, 0xd3, 0x07, 0x7d, 0x2c, 0xe5, 0x11, 0x73, 0x69, 0xa9, 0x08,
	0x41, 0x61, 0x5b, 0xe2, 0x45, 0x7a, 0x10, 0x2c, 0x08, 0x7a, 0x10, 0x4a, 0xbc, 0x79, 0x29, 0x93,
	0x64, 0xac, 0xc1, 0x34, 0x1b, 0x37, 0x9b, 0xc2, 0xfe, 0x23, 0x7f, 0xa6, 0x74, 0x93, 0x06, 0x0f,
	0xde, 0xbe, 0xf9, 0xbe, 0x6f, 0xe6, 0xdb, 0x9d, 0x01, 0xaf, 0xc0, 0x44, 0x56, 0xdb, 0x9d, 0x2a,
	0x93, 0x6d, 0x45, 0xea, 0x90, 0x25, 0x24, 0x4a, 0x25, 0xb5, 0xf4, 0xcf, 0x76, 0x52, 0xee, 0x72,
	0x5a, 0xd8, 0x2a, 0xae, 0x5f, 0x17, 0x58, 0x98, 0x46, 0x9a, 0x7f, 0x32, 0x70, 0x9f, 0x48, 0x63,
	0x8a, 0x1a, 0x39, 0x87, 0x81, 0x36, 0x25, 0x79, 0xfd, 0x19, 0x0b, 0x86, 0x91, 0xc5, 0xdc, 0x07,
	0x37, 0xc9, 0x33, 0x2a, 0xf4, 0x63, 0xe9, 0xb9, 0x96, 0xef, 0x6a, 0xbe, 0x04, 0xe7, 0x8d, 0x30,
	0x25, 0x55, 0x79, 0xce, 0xac, 0x1f, 0x8c, 0xc2, 0xff, 0xe2, 0x34, 0x4b, 0x3c, 0x34, 0xc2, 0x7d,
	0xa1, 0x95, 0x89, 0x4e, 0x36, 0x7f, 0x05, 0x7f, 0xbf, 0x0b, 0xfc, 0x1f, 0xf4, 0xdf, 0xc9, 0x78,
	0xcc, 0x0e, 0x3e, 0x42, 0x3e, 0x81, 0xdf, 0x07, 0xcc, 0x6b, 0xf2, 0x7a, 0x96, 0x6b, 0x8a, 0x55,
	0xef, 0x86, 0xcd, 0x5f, 0xc0, 0xd9, 0xa0, 0xc9, 0x25, 0xa6, 0xfc, 0x02, 0xdc, 0x7d, 0x1b, 0x64,
	0x7d, 0xa3, 0x70, 0xd8, 0x25, 0x47, 0x9d, 0xc4, 0x03, 0x18, 0xc4, 0x32, 0x35, 0xf6, 0x3f, 0xa3,
	0x70, 0x22, 0x9a, 0x35, 0x88, 0xd3, 0x1a, 0xc4, 0x5d, 0x61, 0x22, 0xeb, 0x08, 0x2f, 0xc1, 0x89,
	0xe8, 0xa3, 0xa6, 0x4a, 0xf3, 0x29, 0x38, 0xaa, 0x85, 0xae, 0x68, 0x03, 0xfd, 0x0e, 0xcd, 0x7f,
	0x85, 0xb7, 0x30, 0x5e, 0x67, 0xad, 0xfb, 0x59, 0x2b, 0xc2, 0x3d, 0xbf, 0x82, 0x71, 0xdb, 0xb3,
	0xce, 0x5a, 0xea, 0xc7, 0xde, 0x80, 0x2d, 0xd9, 0xfa, 0x1c, 0xa6, 0x89, 0xdc, 0x0b, 0xcc, 0xb3,
	0x18, 0x63, 0x14, 0xf6, 0x6a, 0x02, 0xcb, 0x4c, 0x1c, 0x2f, 0x27, 0xb0, 0xd6, 0x72, 0xc3, 0xe2,
	0x3f, 0xf6, 0x91, 0xd7, 0x5f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x13, 0x77, 0xbe, 0x6d, 0xd5, 0x01,
	0x00, 0x00,
}
