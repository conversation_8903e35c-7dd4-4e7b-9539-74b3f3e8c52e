package bls

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetClient(ctx context.CsmContext, endpoint string) error
	GetClientWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error

	CreateHostingBlsTask(logConf *meta.LogConf) (*TaskCreationResponse, error)
	UpdateHostingBlsTask(logConf *meta.LogConf, taskID string) error
	DeleteHostingBlsTask(taskID string) error
	StartHostingBlsTask(taskID string) error
	PauseHostingBlsTask(taskID string) error
	GetHostingBlsTaskDetail(taskID string) (*TaskDetailResponse, error)
	CreateAIGWBlsTask(body blsv3.TaskCreationRequestBody) (*blsv3.TaskID, error)
	GetBlsTaskInstance(taskID string) (*blsv3.TaskInstanceResponseParameters, error)
	PostBlsTaskAction(taskId, action string) error
}
