package bls

import (
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
)

func (c *Client) CreateHostingBlsTask(logConf *meta.LogConf) (*TaskCreationResponse, error) {
	result := &TaskCreationResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsCreateTaskURI()).
		WithBody(GenerateTaskCreationRequestBody(logConf)).
		WithResult(result).
		Do()

	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) CreateAIGWBlsTask(body blsv3.TaskCreationRequestBody) (*blsv3.TaskID, error) {
	res := &blsv3.TaskID{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(blsv3.GetBlsCreateTaskActionURL()).
		WithBody(body).
		WithResult(res).
		Do()
	if err != nil {
		return res, err
	}
	return res, nil
}

func (c *Client) GetBlsTaskInstance(taskID string) (*blsv3.TaskInstanceResponseParameters, error) {
	res := &blsv3.TaskInstanceResponseParameters{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(blsv3.GetBlsGetTaskInstanceURL(taskID)).
		WithResult(res).
		Do()
	if err != nil {
		return res, err
	}
	return res, nil
}

func (c *Client) PostBlsTaskAction(taskId, action string) error {
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(blsv3.GetBlsPostTaskActionURL(taskId)).
		WithQueryParamFilter(action, " ").
		Do()
	return err
}

func (c *Client) UpdateHostingBlsTask(logConf *meta.LogConf, taskID string) error {
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsUpdateTaskURI(taskID)).
		WithBody(GenerateTaskCreationRequestBody(logConf)).
		Do()

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) DeleteHostingBlsTask(taskID string) error {
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsDeleteTaskURI(taskID)).
		Do()

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) StartHostingBlsTask(taskID string) error {
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsStartTaskURI(taskID)).
		Do()

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) PauseHostingBlsTask(taskID string) error {
	err := bce.NewRequestBuilder(c).
		WithMethod(http.POST).
		WithURL(GetBlsPauseTaskURI(taskID)).
		Do()

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) GetHostingBlsTaskDetail(taskID string) (*TaskDetailResponse, error) {
	result := &TaskDetailResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(GetBlsDetailTaskURI(taskID)).
		WithResult(result).
		Do()

	if err != nil {
		return nil, err
	}

	return result, nil
}
