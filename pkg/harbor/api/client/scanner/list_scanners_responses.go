// Code generated by go-swagger; DO NOT EDIT.

package scanner

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// ListScannersReader is a Reader for the ListScanners structure.
type ListScannersReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListScannersReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListScannersOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListScannersBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewListScannersUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 403:
		result := NewListScannersForbidden()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewListScannersInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewListScannersOK creates a ListScannersOK with default headers values
func NewListScannersOK() *ListScannersOK {
	return &ListScannersOK{}
}

/* ListScannersOK describes a response with status code 200, with default header values.

A list of scanner registrations.
*/
type ListScannersOK struct {

	/* Link to previous page and next page
	 */
	Link string

	/* The total count of available items
	 */
	XTotalCount int64

	Payload []*model.ScannerRegistration
}

func (o *ListScannersOK) Error() string {
	return fmt.Sprintf("[GET /scanners][%d] listScannersOK  %+v", 200, o.Payload)
}
func (o *ListScannersOK) GetPayload() []*model.ScannerRegistration {
	return o.Payload
}

func (o *ListScannersOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header Link
	hdrLink := response.GetHeader("Link")

	if hdrLink != "" {
		o.Link = hdrLink
	}

	// hydrates response header X-Total-Count
	hdrXTotalCount := response.GetHeader("X-Total-Count")

	if hdrXTotalCount != "" {
		valxTotalCount, err := swag.ConvertInt64(hdrXTotalCount)
		if err != nil {
			return errors.InvalidType("X-Total-Count", "header", "int64", hdrXTotalCount)
		}
		o.XTotalCount = valxTotalCount
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListScannersBadRequest creates a ListScannersBadRequest with default headers values
func NewListScannersBadRequest() *ListScannersBadRequest {
	return &ListScannersBadRequest{}
}

/* ListScannersBadRequest describes a response with status code 400, with default header values.

Bad request
*/
type ListScannersBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *ListScannersBadRequest) Error() string {
	return fmt.Sprintf("[GET /scanners][%d] listScannersBadRequest  %+v", 400, o.Payload)
}
func (o *ListScannersBadRequest) GetPayload() *model.Errors {
	return o.Payload
}

func (o *ListScannersBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListScannersUnauthorized creates a ListScannersUnauthorized with default headers values
func NewListScannersUnauthorized() *ListScannersUnauthorized {
	return &ListScannersUnauthorized{}
}

/* ListScannersUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type ListScannersUnauthorized struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *ListScannersUnauthorized) Error() string {
	return fmt.Sprintf("[GET /scanners][%d] listScannersUnauthorized  %+v", 401, o.Payload)
}
func (o *ListScannersUnauthorized) GetPayload() *model.Errors {
	return o.Payload
}

func (o *ListScannersUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListScannersForbidden creates a ListScannersForbidden with default headers values
func NewListScannersForbidden() *ListScannersForbidden {
	return &ListScannersForbidden{}
}

/* ListScannersForbidden describes a response with status code 403, with default header values.

Forbidden
*/
type ListScannersForbidden struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *ListScannersForbidden) Error() string {
	return fmt.Sprintf("[GET /scanners][%d] listScannersForbidden  %+v", 403, o.Payload)
}
func (o *ListScannersForbidden) GetPayload() *model.Errors {
	return o.Payload
}

func (o *ListScannersForbidden) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListScannersInternalServerError creates a ListScannersInternalServerError with default headers values
func NewListScannersInternalServerError() *ListScannersInternalServerError {
	return &ListScannersInternalServerError{}
}

/* ListScannersInternalServerError describes a response with status code 500, with default header values.

Internal server error
*/
type ListScannersInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *ListScannersInternalServerError) Error() string {
	return fmt.Sprintf("[GET /scanners][%d] listScannersInternalServerError  %+v", 500, o.Payload)
}
func (o *ListScannersInternalServerError) GetPayload() *model.Errors {
	return o.Payload
}

func (o *ListScannersInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
