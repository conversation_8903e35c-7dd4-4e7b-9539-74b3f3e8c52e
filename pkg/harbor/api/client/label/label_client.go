// Code generated by go-swagger; DO NOT EDIT.

package label

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new label API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for label API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockLabelClientService

// ClientService is the interface for Client methods
type ClientService interface {
	CreateLabel(params *CreateLabelParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*CreateLabelCreated, error)

	DeleteLabel(params *DeleteLabelParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteLabelOK, error)

	GetLabelByID(params *GetLabelByIDParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetLabelByIDOK, error)

	ListLabels(params *ListLabelsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListLabelsOK, error)

	UpdateLabel(params *UpdateLabelParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateLabelOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  CreateLabel posts creates a label

  This endpoint let user creates a label.

*/
func (a *Client) CreateLabel(params *CreateLabelParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*CreateLabelCreated, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewCreateLabelParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "CreateLabel",
		Method:             "POST",
		PathPattern:        "/labels",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &CreateLabelReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*CreateLabelCreated)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for CreateLabel: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  DeleteLabel deletes the label specified by ID

  Delete the label specified by ID.

*/
func (a *Client) DeleteLabel(params *DeleteLabelParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteLabelOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewDeleteLabelParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "DeleteLabel",
		Method:             "DELETE",
		PathPattern:        "/labels/{label_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &DeleteLabelReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*DeleteLabelOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for DeleteLabel: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  GetLabelByID gets the label specified by ID

  This endpoint let user get the label by specific ID.

*/
func (a *Client) GetLabelByID(params *GetLabelByIDParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetLabelByIDOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetLabelByIDParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "GetLabelByID",
		Method:             "GET",
		PathPattern:        "/labels/{label_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetLabelByIDReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetLabelByIDOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for GetLabelByID: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  ListLabels lists labels according to the query strings

  This endpoint let user list labels by name, scope and project_id

*/
func (a *Client) ListLabels(params *ListLabelsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListLabelsOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListLabelsParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "ListLabels",
		Method:             "GET",
		PathPattern:        "/labels",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListLabelsReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListLabelsOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for ListLabels: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  UpdateLabel updates the label properties

  This endpoint let user update label properties.

*/
func (a *Client) UpdateLabel(params *UpdateLabelParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateLabelOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewUpdateLabelParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "UpdateLabel",
		Method:             "PUT",
		PathPattern:        "/labels/{label_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &UpdateLabelReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*UpdateLabelOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for UpdateLabel: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
