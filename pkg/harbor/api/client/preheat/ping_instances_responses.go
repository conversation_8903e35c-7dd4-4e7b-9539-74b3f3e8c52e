// Code generated by go-swagger; DO NOT EDIT.

package preheat

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// PingInstancesReader is a Reader for the PingInstances structure.
type PingInstancesReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PingInstancesReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewPingInstancesOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPingInstancesBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewPingInstancesUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewPingInstancesNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewPingInstancesInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewPingInstancesOK creates a PingInstancesOK with default headers values
func NewPingInstancesOK() *PingInstancesOK {
	return &PingInstancesOK{}
}

/* PingInstancesOK describes a response with status code 200, with default header values.

Success
*/
type PingInstancesOK struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string
}

func (o *PingInstancesOK) Error() string {
	return fmt.Sprintf("[POST /p2p/preheat/instances/ping][%d] pingInstancesOK ", 200)
}

func (o *PingInstancesOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	return nil
}

// NewPingInstancesBadRequest creates a PingInstancesBadRequest with default headers values
func NewPingInstancesBadRequest() *PingInstancesBadRequest {
	return &PingInstancesBadRequest{}
}

/* PingInstancesBadRequest describes a response with status code 400, with default header values.

Bad request
*/
type PingInstancesBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingInstancesBadRequest) Error() string {
	return fmt.Sprintf("[POST /p2p/preheat/instances/ping][%d] pingInstancesBadRequest  %+v", 400, o.Payload)
}
func (o *PingInstancesBadRequest) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingInstancesBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingInstancesUnauthorized creates a PingInstancesUnauthorized with default headers values
func NewPingInstancesUnauthorized() *PingInstancesUnauthorized {
	return &PingInstancesUnauthorized{}
}

/* PingInstancesUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type PingInstancesUnauthorized struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingInstancesUnauthorized) Error() string {
	return fmt.Sprintf("[POST /p2p/preheat/instances/ping][%d] pingInstancesUnauthorized  %+v", 401, o.Payload)
}
func (o *PingInstancesUnauthorized) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingInstancesUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingInstancesNotFound creates a PingInstancesNotFound with default headers values
func NewPingInstancesNotFound() *PingInstancesNotFound {
	return &PingInstancesNotFound{}
}

/* PingInstancesNotFound describes a response with status code 404, with default header values.

Instance not found (when instance is provided by ID).
*/
type PingInstancesNotFound struct {
}

func (o *PingInstancesNotFound) Error() string {
	return fmt.Sprintf("[POST /p2p/preheat/instances/ping][%d] pingInstancesNotFound ", 404)
}

func (o *PingInstancesNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewPingInstancesInternalServerError creates a PingInstancesInternalServerError with default headers values
func NewPingInstancesInternalServerError() *PingInstancesInternalServerError {
	return &PingInstancesInternalServerError{}
}

/* PingInstancesInternalServerError describes a response with status code 500, with default header values.

Internal server error
*/
type PingInstancesInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingInstancesInternalServerError) Error() string {
	return fmt.Sprintf("[POST /p2p/preheat/instances/ping][%d] pingInstancesInternalServerError  %+v", 500, o.Payload)
}
func (o *PingInstancesInternalServerError) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingInstancesInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
