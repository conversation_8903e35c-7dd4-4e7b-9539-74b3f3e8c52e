package event

import (
	"net/http"
	"strings"
	"time"

	"github.com/goharbor/harbor/src/common/security"
	"github.com/goharbor/harbor/src/lib"
	"github.com/goharbor/harbor/src/pkg/notification"
	"github.com/goharbor/harbor/src/server/middleware"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/controller/event/metadata"
)

// push image failed
// acceleration success, failed
// push image:
//
//	head blob
//	post blob
//	patch blob
//	put blob
//	put manifest
func Middleware(skippers ...middleware.Skipper) func(http.Handler) http.Handler {
	return middleware.AfterResponse(func(w http.ResponseWriter, r *http.Request, statusCode int) error {
		// success checks whether the status code is <= 399 & !=401
		if statusCode < http.StatusBadRequest || statusCode == http.StatusUnauthorized {
			return nil
		}

		var operator string
		secCtx, existed := security.FromContext(r.Context())
		if existed && secCtx != nil {
			operator = secCtx.GetUsername()
		}

		artInfo := lib.GetArtifactInfo(r.Context())

		_, matchBlobUploadUrl := lib.MatchBlobUploadURLPattern(r.URL.Path)
		_, _, matchManifest := lib.MatchManifestURLPattern(r.URL.Path)
		_, _, matchBlobURL := lib.MatchBlobURLPattern(r.URL.Path)

		if matchBlobUploadUrl ||
			(matchBlobURL && !strings.EqualFold(r.Method, "GET")) ||
			(matchManifest && strings.EqualFold(r.Method, "PUT")) {
			notification.AddEvent(r.Context(), &metadata.PushArtifactFailedMetadata{
				Repo:     artInfo.Repository,
				Project:  artInfo.ProjectName,
				Operator: operator,
				OccurAt:  time.Now(),
				RespCode: statusCode,
			}, true)
		}

		return nil
	}, skippers...)
}
