// Code generated by go-swagger; DO NOT EDIT.

package products

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new products API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for products API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockProductsClientService

// ClientService is the interface for Client methods
type ClientService interface {
	DeleteChartrepoRepoChartsNameVersionLabelsID(params *DeleteChartrepoRepoChartsNameVersionLabelsIDParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteChartrepoRepoChartsNameVersionLabelsIDOK, error)

	GetChartrepoRepoChartsNameVersionLabels(params *GetChartrepoRepoChartsNameVersionLabelsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetChartrepoRepoChartsNameVersionLabelsOK, error)

	PostChartrepoRepoChartsNameVersionLabels(params *PostChartrepoRepoChartsNameVersionLabelsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*PostChartrepoRepoChartsNameVersionLabelsOK, error)

	PostEmailPing(params *PostEmailPingParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*PostEmailPingOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  DeleteChartrepoRepoChartsNameVersionLabelsID removes label from chart

  Remove label from the specified chart version.
*/
func (a *Client) DeleteChartrepoRepoChartsNameVersionLabelsID(params *DeleteChartrepoRepoChartsNameVersionLabelsIDParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteChartrepoRepoChartsNameVersionLabelsIDOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewDeleteChartrepoRepoChartsNameVersionLabelsIDParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "DeleteChartrepoRepoChartsNameVersionLabelsID",
		Method:             "DELETE",
		PathPattern:        "/chartrepo/{repo}/charts/{name}/{version}/labels/{id}",
		ProducesMediaTypes: []string{"application/json", "text/plain"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &DeleteChartrepoRepoChartsNameVersionLabelsIDReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*DeleteChartrepoRepoChartsNameVersionLabelsIDOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for DeleteChartrepoRepoChartsNameVersionLabelsID: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  GetChartrepoRepoChartsNameVersionLabels returns the attahced labels of chart

  Return the attahced labels of the specified chart version.
*/
func (a *Client) GetChartrepoRepoChartsNameVersionLabels(params *GetChartrepoRepoChartsNameVersionLabelsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetChartrepoRepoChartsNameVersionLabelsOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetChartrepoRepoChartsNameVersionLabelsParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "GetChartrepoRepoChartsNameVersionLabels",
		Method:             "GET",
		PathPattern:        "/chartrepo/{repo}/charts/{name}/{version}/labels",
		ProducesMediaTypes: []string{"application/json", "text/plain"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetChartrepoRepoChartsNameVersionLabelsReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetChartrepoRepoChartsNameVersionLabelsOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for GetChartrepoRepoChartsNameVersionLabels: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  PostChartrepoRepoChartsNameVersionLabels marks label to chart

  Mark label to the specified chart version.
*/
func (a *Client) PostChartrepoRepoChartsNameVersionLabels(params *PostChartrepoRepoChartsNameVersionLabelsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*PostChartrepoRepoChartsNameVersionLabelsOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewPostChartrepoRepoChartsNameVersionLabelsParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "PostChartrepoRepoChartsNameVersionLabels",
		Method:             "POST",
		PathPattern:        "/chartrepo/{repo}/charts/{name}/{version}/labels",
		ProducesMediaTypes: []string{"application/json", "text/plain"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &PostChartrepoRepoChartsNameVersionLabelsReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*PostChartrepoRepoChartsNameVersionLabelsOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for PostChartrepoRepoChartsNameVersionLabels: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  PostEmailPing tests connection and authentication with email server

  Test connection and authentication with email server.

*/
func (a *Client) PostEmailPing(params *PostEmailPingParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*PostEmailPingOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewPostEmailPingParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "PostEmailPing",
		Method:             "POST",
		PathPattern:        "/email/ping",
		ProducesMediaTypes: []string{"application/json", "text/plain"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &PostEmailPingReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*PostEmailPingOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for PostEmailPing: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
