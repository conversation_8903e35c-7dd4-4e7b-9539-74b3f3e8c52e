// Code generated by go-swagger; DO NOT EDIT.

package accelerator

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// CreateAcceleratorPolicyReader is a Reader for the CreateAcceleratorPolicy structure.
type CreateAcceleratorPolicyReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *CreateAcceleratorPolicyReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewCreateAcceleratorPolicyOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewCreateAcceleratorPolicyBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 409:
		result := NewCreateAcceleratorPolicyConflict()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewCreateAcceleratorPolicyInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewCreateAcceleratorPolicyOK creates a CreateAcceleratorPolicyOK with default headers values
func NewCreateAcceleratorPolicyOK() *CreateAcceleratorPolicyOK {
	return &CreateAcceleratorPolicyOK{}
}

/* CreateAcceleratorPolicyOK describes a response with status code 200, with default header values.

OK
*/
type CreateAcceleratorPolicyOK struct {
	Payload int64
}

func (o *CreateAcceleratorPolicyOK) Error() string {
	return fmt.Sprintf("[POST /accelerators/policies][%d] createAcceleratorPolicyOK  %+v", 200, o.Payload)
}
func (o *CreateAcceleratorPolicyOK) GetPayload() int64 {
	return o.Payload
}

func (o *CreateAcceleratorPolicyOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCreateAcceleratorPolicyBadRequest creates a CreateAcceleratorPolicyBadRequest with default headers values
func NewCreateAcceleratorPolicyBadRequest() *CreateAcceleratorPolicyBadRequest {
	return &CreateAcceleratorPolicyBadRequest{}
}

/* CreateAcceleratorPolicyBadRequest describes a response with status code 400, with default header values.

Bad Request
*/
type CreateAcceleratorPolicyBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *CreateAcceleratorPolicyBadRequest) Error() string {
	return fmt.Sprintf("[POST /accelerators/policies][%d] createAcceleratorPolicyBadRequest  %+v", 400, o.Payload)
}
func (o *CreateAcceleratorPolicyBadRequest) GetPayload() string {
	return o.Payload
}

func (o *CreateAcceleratorPolicyBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCreateAcceleratorPolicyConflict creates a CreateAcceleratorPolicyConflict with default headers values
func NewCreateAcceleratorPolicyConflict() *CreateAcceleratorPolicyConflict {
	return &CreateAcceleratorPolicyConflict{}
}

/* CreateAcceleratorPolicyConflict describes a response with status code 409, with default header values.

Conflict
*/
type CreateAcceleratorPolicyConflict struct {
	Payload string
}

func (o *CreateAcceleratorPolicyConflict) Error() string {
	return fmt.Sprintf("[POST /accelerators/policies][%d] createAcceleratorPolicyConflict  %+v", 409, o.Payload)
}
func (o *CreateAcceleratorPolicyConflict) GetPayload() string {
	return o.Payload
}

func (o *CreateAcceleratorPolicyConflict) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCreateAcceleratorPolicyInternalServerError creates a CreateAcceleratorPolicyInternalServerError with default headers values
func NewCreateAcceleratorPolicyInternalServerError() *CreateAcceleratorPolicyInternalServerError {
	return &CreateAcceleratorPolicyInternalServerError{}
}

/* CreateAcceleratorPolicyInternalServerError describes a response with status code 500, with default header values.

Internal Server Error
*/
type CreateAcceleratorPolicyInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *CreateAcceleratorPolicyInternalServerError) Error() string {
	return fmt.Sprintf("[POST /accelerators/policies][%d] createAcceleratorPolicyInternalServerError  %+v", 500, o.Payload)
}
func (o *CreateAcceleratorPolicyInternalServerError) GetPayload() string {
	return o.Payload
}

func (o *CreateAcceleratorPolicyInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
