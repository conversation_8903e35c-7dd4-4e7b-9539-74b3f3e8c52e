// Code generated by go-swagger; DO NOT EDIT.

package product

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// GetProductsStatisticReader is a Reader for the GetProductsStatistic structure.
type GetProductsStatisticReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetProductsStatisticReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetProductsStatisticOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 500:
		result := NewGetProductsStatisticInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewGetProductsStatisticOK creates a GetProductsStatisticOK with default headers values
func NewGetProductsStatisticOK() *GetProductsStatisticOK {
	return &GetProductsStatisticOK{}
}

/* GetProductsStatisticOK describes a response with status code 200, with default header values.

OK
*/
type GetProductsStatisticOK struct {
	Payload *model.ModelsStatistic
}

func (o *GetProductsStatisticOK) Error() string {
	return fmt.Sprintf("[GET /products/statistic][%d] getProductsStatisticOK  %+v", 200, o.Payload)
}
func (o *GetProductsStatisticOK) GetPayload() *model.ModelsStatistic {
	return o.Payload
}

func (o *GetProductsStatisticOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(model.ModelsStatistic)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProductsStatisticInternalServerError creates a GetProductsStatisticInternalServerError with default headers values
func NewGetProductsStatisticInternalServerError() *GetProductsStatisticInternalServerError {
	return &GetProductsStatisticInternalServerError{}
}

/* GetProductsStatisticInternalServerError describes a response with status code 500, with default header values.

Internal Server Error
*/
type GetProductsStatisticInternalServerError struct {
	Payload string
}

func (o *GetProductsStatisticInternalServerError) Error() string {
	return fmt.Sprintf("[GET /products/statistic][%d] getProductsStatisticInternalServerError  %+v", 500, o.Payload)
}
func (o *GetProductsStatisticInternalServerError) GetPayload() string {
	return o.Payload
}

func (o *GetProductsStatisticInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
