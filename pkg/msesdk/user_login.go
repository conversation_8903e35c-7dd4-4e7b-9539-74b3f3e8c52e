package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"os"
)

var _ Request = &UserLoginRequest{}

type UserLoginRequest struct {
	Owner    string `json:"-"`
	Name     string `json:"name"`
	Password string `json:"password"`
}

type UserLoginResponse struct {
	Token         string `json:"token"`
	Owner         string `json:"owner"`
	ServerId      string `json:"serverId"`
	ServerRelease string `json:"serverRelease"`
	UserId        string `json:"userId"`        // 新增用户ID字段
	UserName      string `json:"userName"`      // 新增用户名字段
	UserRole      string `json:"userRole"`      // 新增用户角色字段
}

func (r *UserLoginRequest) ApiName() string {
	return "UserLogin"
}

func (r *UserLoginRequest) FillAndValidate(release string) error {
	return nil
}

func (r *UserLoginRequest) Method() string { return http.MethodPost }

func (r *UserLoginRequest) URI() string { return "/core/v1/user/login" }

func (r *UserLoginRequest) QueryParams() map[string]string { return nil }

func (r *UserLoginRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal(map[string]string{
		"owner":    r.Owner,
		"name":     r.Name,
		"password": r.Password,
	})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *UserLoginRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *UserLoginRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.Response)
	if !ok {
		return nil, fmt.Errorf("parse reset admin password response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	loginResp := rawResponse.GetLoginResponse()
	response := &UserLoginResponse{
		Token:         loginResp.GetToken().GetValue(),
		Owner:         r.Owner,
		ServerId:      os.Getenv("REGISTRY_SERVER_ID"),
		ServerRelease: os.Getenv("REGISTRY_RELEASE"),
		UserId:        loginResp.GetUserId().GetValue(),        // 从Polaris响应中获取用户ID
		UserName:      loginResp.GetName().GetValue(),          // 从Polaris响应中获取用户名
		UserRole:      loginResp.GetRole().GetValue(),          // 从Polaris响应中获取用户角色
	}
	return response, nil
}
