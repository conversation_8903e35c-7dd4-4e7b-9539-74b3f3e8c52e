package msesdk

import (
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
)

// GetUserTokenRequest 获取用户Token请求结构体
type GetUserTokenRequest struct {
	ID string `json:"id" binding:"required"` // 用户ID，必填
}

// ApiName 返回API名称
func (r *GetUserTokenRequest) ApiName() string {
	return "GetUserToken"
}

// FillAndValidate 填充和验证请求参数
func (r *GetUserTokenRequest) FillAndValidate(release string) error {
	if r.ID == "" {
		return fmt.Errorf("user id is required")
	}
	return nil
}

// Method 返回HTTP请求方法
func (r *GetUserTokenRequest) Method() string {
	return http.MethodGet
}

// Path 返回请求路径
func (r *GetUserTokenRequest) Path(release string) string {
	return "/core/v1/user/token"
}

// URI 返回请求URI
func (r *GetUserTokenRequest) URI() string {
	return "/core/v1/user/token"
}

// QueryParams 返回查询参数
func (r *GetUserTokenRequest) QueryParams() map[string]string {
	return map[string]string{
		"id": r.ID,
	}
}

// Query 构建查询参数
func (r *GetUserTokenRequest) Query(release string) (url.Values, error) {
	query := url.Values{}
	query.Set("id", r.ID)
	return query, nil
}

// Body 构建请求体（GET请求无需请求体）
func (r *GetUserTokenRequest) Body(release string) (io.Reader, error) {
	return nil, nil
}

// Headers 返回请求头
func (r *GetUserTokenRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

// IsWriteRequest 判断是否为写请求
func (r *GetUserTokenRequest) IsWriteRequest() bool {
	return false // GET请求为读请求
}

// GetUserTokenResponse 获取用户Token响应结构
type GetUserTokenResponse struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	AuthToken   string `json:"auth_token"`
	TokenEnable bool   `json:"token_enable"`
}

// ParseResponse 解析响应
func (r *GetUserTokenRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.Response)
	if !ok {
		return nil, fmt.Errorf("parse get user token response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	// 提取用户信息并转换为MSE标准格式
	user := rawResponse.GetUser()
	if user == nil {
		return nil, fmt.Errorf("user data not found in response")
	}

	response := &GetUserTokenResponse{
		ID:          user.GetId().GetValue(),
		Name:        user.GetName().GetValue(),
		AuthToken:   user.GetAuthToken().GetValue(),
		TokenEnable: user.GetTokenEnable().GetValue(),
	}

	return response, nil
}


