package aigateway

import (
	meta1 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	GetAllBlsTasksByInstanceUUID(ctx csmContext.CsmContext, instanceUUID string) (*[]meta1.AigwBls, error)
	NewBlsTask(ctx csmContext.CsmContext, bls *meta1.AigwBls) error
	DeleteBlsTaskByInstanceUUID(ctx csmContext.CsmContext, instanceUUID string) error
}
