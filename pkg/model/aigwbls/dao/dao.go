package dao

import (
	"reflect"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// AigwBlsDao AIGW BLS数据访问对象
type AigwBlsDao struct {
	*dao.Dao
}

// NewAigwBlsDao 创建新的AIGW BLS DAO
func NewAigwBlsDao(db *dbutil.DB) *AigwBlsDao {
	t := reflect.TypeOf(meta.AigwBls{})
	return &AigwBlsDao{
		Dao: dao.NewDao(t, db),
	}
}
