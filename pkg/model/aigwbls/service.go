package aigateway

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigwbls/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
	opt *Option
	dao model.NewAIGatewayBlsDaoInterface
}

func NewAIGatewayBlsService(option *Option) *Service {
	return &Service{
		opt: option,
		dao: dao.NewAigwBlsDao(option.DB),
	}
}

// WithTx 网关实例 tx
// WithTx 网关实例bls tx支持事务
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	nOpt := *s.opt
	nOpt.DB = tx
	return NewAIGatewayBlsService(&nOpt)
}

// GetAllBlsTasksByInstanceUUID 根据实例UUID获取所有未删除的BLS任务
func (s *Service) GetAllBlsTasksByInstanceUUID(ctx csmContext.CsmContext, instanceUUID string) (*[]meta.AigwBls, error) {
	accountId, _ := iam.GetAccountId(ctx)

	where := &meta.AigwBls{
		InstanceUUID: instanceUUID,
		AccountID:    accountId,
		Deleted:      csm.Int(0),
	}
	not := &meta.AigwBls{}

	tasks, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	if tasks == nil {
		return nil, nil
	}
	return tasks.(*[]meta.AigwBls), nil
}

// NewBlsTask 创建新的BLS任务记录
func (s *Service) NewBlsTask(ctx csmContext.CsmContext, bls *meta.AigwBls) error {
	return s.dao.Save(ctx, bls)
}

// DeleteBlsTaskByInstanceUUID 根据实例UUID软删除BLS任务
func (s *Service) DeleteBlsTaskByInstanceUUID(ctx csmContext.CsmContext, instanceUUID string) error {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.AigwBls{
		InstanceUUID: instanceUUID,
		AccountID:    accountId,
	}
	return s.dao.BatchDelete(ctx, where)
}

// DeleteBlsTaskByTaskID 根据任务ID软删除BLS任务
func (s *Service) DeleteBlsTaskByTaskID(ctx csmContext.CsmContext, taskID string) error {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.AigwBls{
		TaskID:    taskID,
		AccountID: accountId,
	}
	return s.dao.BatchDelete(ctx, where)
}

// GetBlsTaskByTaskId 根据任务ID获取BLS任务信息
func (s *Service) GetBlsTaskByTaskId(ctx csmContext.CsmContext, taskId string) (*meta.AigwBls, error) {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.AigwBls{
		TaskID:    taskId,
		AccountID: accountId,
	}
	not := &meta.AigwBls{}
	task, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	if task == nil {
		return nil, nil
	}
	return task.(*meta.AigwBls), nil
}
