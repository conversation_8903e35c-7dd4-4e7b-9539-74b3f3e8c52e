package bls

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/bls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// VIPs - BLS各地域VIPs
var VIPs = map[string]string{
	"bj": "http://**************:8085",
	"gz": "http://************:8085",
}

const (
	BlsTaskDefaultTTL = 3 // 收集创建任务前n天的日志
	PodNameKey        = "io.kubernetes.pod.name"
	PodNameValue      = "higress-gateway-.*"
	PodNamespaceKey   = "io.kubernetes.pod.namespace"
)

type Service struct {
	option    *Option
	blsClient bls.ServiceInterface
}

func NewService(opt *Option) *Service {
	return &Service{
		option:    opt,
		blsClient: &bls.Client{},
	}
}

func (s *Service) CreateHostingBlsTask(ctx csmContext.CsmContext, logConf *meta.LogConf, region string) (string, error) {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return "", err
	}
	result, err := s.blsClient.CreateHostingBlsTask(logConf)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to create bls task: %v", err)
		return "", err
	}
	return result.TaskID, nil
}

func (s *Service) GetAIGWBlsTasksInstanceByTaskID(ctx csmContext.CsmContext, taskId, region string) (
	*blsv3.TaskInstanceResponseParameters, error) {
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return nil, err
	}
	result, err := s.blsClient.GetBlsTaskInstance(taskId)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to create bls task: %v", err)
		return nil, err
	}
	return result, nil
}

func (s *Service) CloseAIGWBlsTasksInstanceByTaskID(ctx csmContext.CsmContext, taskId, region string) error {
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.PostBlsTaskAction(taskId, "delete")
	if err != nil {
		ctx.CsmLogger().Errorf("fail to create bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) CreateAIGWTask(ctx csmContext.CsmContext, logConf *blsv3.LogConf, accountId, region string) (string, error) {
	namespace := "istio-system-" + logConf.InstanceUUID
	body := GenerateAIGWTaskCreationRequestBody(logConf, accountId, namespace)
	// 新建传输任务
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return "", err
	}
	result, err := s.blsClient.CreateAIGWBlsTask(body)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to create bls task: %v", err)
		return "", err
	}

	return result.TaskID, nil
}

func GenerateAIGWTaskCreationRequestBody(logConf *blsv3.LogConf, accountID, namespace string) blsv3.TaskCreationRequestBody {
	return blsv3.TaskCreationRequestBody{
		Name: "task_" + logConf.InstanceUUID + "_" + logConf.ClusterID,
		Config: blsv3.Config{
			SrcConfig: blsv3.SrcConfig{
				SrcType:        "container",
				LogType:        "stdout",
				SrcDir:         "/var/log",
				MatchedPattern: "^.*$",
				IgnorePattern:  "",
				LabelWhite: []blsv3.Label{
					{
						Key:   PodNameKey,
						Value: PodNameValue,
					},
					{
						Key:   PodNamespaceKey,
						Value: namespace,
					},
				},
				ProcessType:  "none",
				LogTime:      "system",
				UseMultiline: false,
				TTL:          BlsTaskDefaultTTL,
			},
			DestConfig: blsv3.DestConfig{
				DestType:  logConf.Type,
				LogStore:  logConf.LogStore,
				AccountID: accountID,
				RateLimit: 1,
			},
		},
	}
}

func (s *Service) UpdateHostingBlsTask(ctx csmContext.CsmContext, logConf *meta.LogConf, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.UpdateHostingBlsTask(logConf, taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) DeleteHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.DeleteHostingBlsTask(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) StartHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.StartHostingBlsTask(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to start bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) PauseHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.PauseHostingBlsTask(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to pause bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) GetHostingBlsTaskDetail(ctx csmContext.CsmContext, taskID, region string) (*meta.Log, error) {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return nil, err
	}
	result, err := s.blsClient.GetHostingBlsTaskDetail(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get the detail of bls task: %s, : %v", taskID, err)
		return nil, err
	}
	res := &meta.Log{
		Enabled: result.Task.Status.Status == string(BlsTaskStatusRunning),
		Type:    result.Task.Config.DestConfig.DestType,
		LogFile: result.Task.Config.DestConfig.LogStore,
	}
	return res, nil
}
