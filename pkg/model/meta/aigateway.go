package meta

import (
	"github.com/asaskevich/govalidator"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// AIGatewayRequest AI网关创建请求结构
type AIGatewayRequest struct {
	Name             string        `json:"name" valid:"required"`
	VpcId            string        `json:"vpcId" valid:"required"`
	VpcCidr          string        `json:"vpcCidr" valid:"required"`
	SubnetId         string        `json:"subnetId" valid:"required"`
	GatewayType      string        `json:"gatewayType" valid:"required"` // small, medium, large
	IsInternal       string        `json:"isInternal"`
	Replicas         int           `json:"replicas" valid:"range(1|5)"`
	Description      string        `json:"description"`
	DeleteProtection bool          `json:"deleteProtection"`
	SrcProduct       string        `json:"srcProduct"`
	Clusters         []ClusterInfo `json:"clusters"`
	BlsEnabled       bool          `json:"blsEnabled"`
	LogStoreName     string        `json:"logStoreName"`
	CpromInstanceId  string        `json:"cpromInstanceId"` // CProm实例ID，用于开启业务指标监控
}

// HigressTemplateData 存储Higress模板渲染所需数据
type HigressTemplateData struct {
	Namespace        string
	AccountId        string
	SubnetId         string
	SecurityGroupIds string
	VpcCidr          string
	IsInternal       string
	Replicas         int
}

// AIGatewayResponse AI网关创建响应结构
type AIGatewayResponse struct {
	Success bool `json:"success"`
	Status  int  `json:"status"`
	Result  struct {
		InstanceId string `json:"instanceId"`
		RequestId  string `json:"requestId"`
		TaskId     string `json:"taskId"`
	} `json:"result"`
}

// AssociateClusterRequest 关联集群请求结构
type AssociateClusterRequest struct {
	Clusters        []ClusterInfo    `json:"clusters"`
	Remark          string           `json:"remark"`
	IngressSettings *IngressSettings `json:"ingressSettings"`
}

// IngressSettings 入口设置
type IngressSettings struct {
	EnableIngress           bool     `json:"enableIngress"`
	EnableAllIngressClasses bool     `json:"enableAllIngressClasses"`
	EnableAllNamespaces     bool     `json:"enableAllNamespaces"`
	IngressClasses          []string `json:"ingressClasses"`
	WatchNamespaces         []string `json:"watchNamespaces"`
}

// AssociateClusterResponse 关联集群响应结构
type AssociateClusterResponse struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
}

// ToGatewayModel 将AI网关请求转换为通用Gateway模型
func (req *AIGatewayRequest) ToGatewayModel() (*AIGatewayInstanceModel, error) {
	if _, validErr := govalidator.ValidateStruct(req); validErr != nil {
		return nil, validErr
	}

	// 根据GatewayType确定资源配额
	resourceQuota := "2c4g" // 默认值
	if req.GatewayType == "medium" {
		resourceQuota = "4c8g"
	} else if req.GatewayType == "large" {
		resourceQuota = "8c16g"
	}

	// 如果replicas未设置，默认为2
	replicas := req.Replicas
	if replicas == 0 {
		replicas = 2
	}

	// 如果DeleteProtection未设置，默认为true
	deleteProtection := req.DeleteProtection
	if !deleteProtection {
		deleteProtection = true
	}

	aigatewayModel := &AIGatewayInstanceModel{
		InstanceUUID:       "",
		AddedClusterID:     "",
		GatewayUUID:        "",
		GatewayName:        req.Name,
		Region:             "",
		AccountID:          "",
		Namespace:          "",
		ResourceQuota:      resourceQuota,
		VpcCIDR:            req.VpcCidr,
		SubnetID:           req.SubnetId,
		SecurityGroupId:    "",
		DeployMode:         "hosting", // 使用托管模式
		Replicas:           replicas,
		SrcProduct:         req.SrcProduct,
		Description:        req.Description,
		DeletionProtection: deleteProtection,
		Deleted:            csm.Int(0),
	}

	return aigatewayModel, nil
}

type AIGatewayInstanceModel struct {
	dbutil.BaseModel

	InstanceUUID      string `gorm:"column:instance_uuid" json:"instance_uuid" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	AddedClusterID    string `gorm:"column:added_cluster_id" json:"added_cluster_id" dbutil:"searchable:wildcard,orderable,updatable"`
	AddedClusterName  string `gorm:"column:added_cluster_name" json:"added_cluster_name" dbutil:"searchable:wildcard,orderable,updatable"`
	GatewayUUID       string `gorm:"column:gateway_uuid" json:"gateway_uuid" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	GatewayName       string `gorm:"column:gateway_name" json:"gateway_name" dbutil:"searchable:wildcard,orderable,updatable"`
	Region            string `gorm:"column:region" json:"region" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	AccountID         string `gorm:"column:account_id" json:"account_id" dbutil:"searchable:wildcard,orderable,updatable"`
	Namespace         string `gorm:"column:namespace" json:"namespace" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	ResourceQuota     string `gorm:"column:resource_quota" json:"resource_quota" dbutil:"updatable"`
	VpcNetworkID      string `gorm:"column:vpc_network_id" json:"vpc_network_id"`
	VpcCIDR           string `gorm:"column:vpc_cidr" json:"vpc_cidr"`
	SubnetID          string `gorm:"column:subnet_id" json:"subnet_id"`
	SubnetCIDR        string `gorm:"column:subnet_cidr" json:"subnet_cidr"`
	SecurityGroupId   string `gorm:"column:security_group_id" json:"security_group_id"`
	DeployMode        string `gorm:"column:deploy_mode" json:"deploy_mode" dbutil:"updatable" valid:"required,in(standalone|hosting)"`
	Replicas          int    `gorm:"column:replicas" json:"replicas" dbutil:"updatable"`
	MonitorEnabled    int    `gorm:"column:monitor_enabled" json:"monitor_enabled"`
	MonitorInstanceID string `gorm:"column:monitor_instance_id" json:"monitor_instance_id"`
	MonitorAgentID    string `gorm:"column:monitor_agent_id" json:"monitor_agent_id"`
	MonitorRegion     string `gorm:"column:monitor_region" json:"monitor_region"`
	MonitorJobID      string `gorm:"column:monitor_job_id" json:"monitor_job_id"`

	AccessAddress         string `gorm:"column:access_address" json:"access_address" dbutil:"updatable"`
	Description           string `gorm:"column:description" json:"description" dbutil:"updatable"`
	Spec                  string `gorm:"column:spec" json:"spec" dbutil:"updatable"`
	PublicAccessible      bool   `gorm:"column:public_accessible" json:"public_accessible" dbutil:"updatable"`
	DeletionProtection    bool   `gorm:"column:delete_protection" json:"delete_protection" dbutil:"updatable"`
	RelationTime          string `gorm:"column:relation_time" json:"relation_time" dbutil:"updatable"`
	Remark                string `gorm:"column:remark" json:"remark" dbutil:"updatable"`
	SrcProduct            string `gorm:"column:src_product" json:"src_product" dbutil:"updatable"`
	EnableIngress         bool   `gorm:"column:enable_ingress" json:"enable_ingress" dbutil:"updatable"`
	EnableAllIngressClass bool   `gorm:"column:enable_all_ingress_class" json:"enable_all_ingress_class" dbutil:"updatable"`
	EnableAllNamespaces   bool   `gorm:"column:enable_all_namespaces" json:"enable_all_namespaces" dbutil:"updatable"`
	IngressClasses        string `gorm:"column:ingress_classes" json:"ingress_classes" dbutil:"updatable"`
	WatchNamespaces       string `gorm:"column:watch_namespaces" json:"watch_namespaces" dbutil:"updatable"`

	HostedClusterID   string `gorm:"column:hosted_cluster_id" json:"hosted_cluster_id" dbutil:"updatable"`
	HostedClusterName string `gorm:"column:hosted_cluster_name" json:"hosted_cluster_name" dbutil:"updatable"`

	BlsEnabled bool `gorm:"column:bls_enabled" json:"bls_enabled" dbutil:"updatable"`
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

func (aigateway *AIGatewayInstanceModel) TableName() string {
	return "t_ai_gateway_instance"
}

// AIGatewayListResponse AI网关实例列表响应结构
type AIGatewayListResponse struct {
	Success bool `json:"success"`
	Status  int  `json:"status"`
	Page    struct {
		OrderBy    string              `json:"orderBy"`
		Order      string              `json:"order"`
		PageNo     int                 `json:"pageNo"`
		PageSize   int                 `json:"pageSize"`
		TotalCount int                 `json:"totalCount"`
		Result     []AIGatewayListItem `json:"result"`
	} `json:"page"`
}

// AIGatewayListItem AI网关实例列表项
type AIGatewayListItem struct {
	InstanceId       string `json:"instanceId"`
	Name             string `json:"name"`
	IngressStatus    string `json:"ingressStatus"`
	Replicas         int    `json:"replicas"`
	VpcCidr          string `json:"vpcCidr"`
	VpcId            string `json:"vpcId"`
	SubnetId         string `json:"subnetId"`
	GatewayType      string `json:"gatewayType"`
	InternalIP       string `json:"internalIP"`
	PublicAccessible bool   `json:"publicAccessible"`
	ExternalIP       string `json:"externalIP"`
	Description      string `json:"description"`
	CreateTime       string `json:"createTime"`
	DeleteProtection bool   `json:"deleteProtection"`
	Region           string `json:"region"`
	Namespace        string `json:"namespace"`
}

// AIGatewayDetailResponse AI网关实例详情响应结构
type AIGatewayDetailResponse struct {
	Success bool      `json:"success"`
	Status  int       `json:"status"`
	Data    AiGateway `json:"data"`
}

// AIGatewayDetailData AI网关实例详情数据
type AIGatewayDetailData struct {
	InstanceId       string        `json:"instanceId"`
	Name             string        `json:"name"`
	Status           string        `json:"status"`
	CreateTime       string        `json:"createTime"`
	Replicas         int           `json:"replicas"`
	VpcId            string        `json:"vpcId"`
	VpcCidr          string        `json:"vpcCidr"`
	SubnetId         string        `json:"subnetId"`
	GatewayType      string        `json:"gatewayType"`
	Region           string        `json:"region"`
	InternalIP       string        `json:"internalIP"`
	PublicAccessible bool          `json:"publicAccessible"`
	ExternalIP       string        `json:"externalIP"`
	DeleteProtection bool          `json:"deleteProtection"`
	Description      string        `json:"description"`
	Namespace        string        `json:"namespace"`
	Clusters         []ClusterInfo `json:"clusters"`
}

// AIGatewayClusterListResponse 查询实例关联的集群列表响应结构
type AIGatewayClusterListResponse struct {
	Success bool                 `json:"success"`
	Status  int                  `json:"status"`
	Page    AIGatewayClusterPage `json:"page"`
}

// AIGatewayClusterPage 集群列表分页结构
type AIGatewayClusterPage struct {
	OrderBy    string                 `json:"orderBy"`
	Order      string                 `json:"order"`
	PageNo     int                    `json:"pageNo"`
	PageSize   int                    `json:"pageSize"`
	TotalCount int                    `json:"totalCount"`
	Result     []AIGatewayClusterItem `json:"result"`
}

// AIGatewayClusterItem 集群信息列表项
type AIGatewayClusterItem struct {
	ClusterId       string           `json:"clusterId"`
	ClusterName     string           `json:"clusterName"`
	Status          string           `json:"status"`
	RelationStatus  string           `json:"relationStatus"`
	Remark          string           `json:"remark"`
	RelationTime    string           `json:"relationTime"`
	UpdateTime      string           `json:"updateTime"`
	IngressSettings *IngressSettings `json:"ingressSettings"`
}

// UpdateAIGatewayRequest 更新AI网关实例的请求结构
type UpdateAIGatewayRequest struct {
	Name             string `json:"name"`
	Description      string `json:"description"`
	DeleteProtection bool   `json:"deleteProtection"`
	PublicAccessible bool   `json:"publicAccessible"`
	Replicas         *int   `json:"replicas"`
}

// UpdateAIGatewayResult 更新AI网关实例的结果结构
type UpdateAIGatewayResult struct {
	InstanceId       string `json:"instanceId"`
	Name             string `json:"name"`
	Description      string `json:"description"`
	DeleteProtection bool   `json:"deleteProtection"`
	PublicAccessible bool   `json:"publicAccessible"`
	Replicas         *int   `json:"replicas,omitempty"` // 网关副本数，使用指针类型支持可选参数
	UpdateTime       string `json:"updateTime"`
}
