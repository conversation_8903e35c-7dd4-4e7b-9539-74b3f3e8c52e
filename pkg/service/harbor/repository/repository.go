package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	tagapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/tag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/project"
	repositoryapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type Interface interface {
	ListRepositories(ctx context.Context, projectName string, repositoryName string, pageNo, pageSize *int64, useSim bool) ([]*ccrmodel.RepositoryResult, int64, error)
	BatchDeleteRepository(ctx context.Context, projectName string, repositoryNames []string) error
	DeleteRepository(ctx context.Context, projectName, repositoryName string) error
	GetRepository(ctx context.Context, projectName, repositoryName string, useSim bool) (*ccrmodel.RepositoryResult, error)
	UpdateRepository(ctx context.Context, projectName, repositoryName, description string, useSim bool) error
	ListAllRepositories(ctx context.Context, nameQ string, pageNo, pageSize int64) ([]*ccrmodel.RepositoryResult, int64, error)
}

var _ Interface = &RepositoryService{}

type RepositoryService struct {
}

func NewRepositoryService() *RepositoryService {
	return &RepositoryService{}
}

// ListRepositories List repositories of the specified project
func (r *RepositoryService) ListRepositories(ctx context.Context, projectName string, repositoryName string, pageNo, pageSize *int64, useSim bool) ([]*ccrmodel.RepositoryResult, int64, error) {
	requestId := gin_context.RequestIdFromContext(ctx)
	logger := gin_context.LoggerFromContext(ctx)
	harborClient := middleware.HarborClientByUserFromContext(ctx, useSim)

	lrp := repositoryapi.NewListRepositoriesParamsWithContext(ctx).WithProjectName(projectName).WithPage(pageNo).WithPageSize(pageSize).WithXRequestID(&requestId)
	if repositoryName != "" {
		rn := fmt.Sprintf("name=~%s", repositoryName)
		lrp.SetQ(&rn)
	}

	resp, err := harborClient.V2Client.Repository.ListRepositories(lrp, harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client call harbor failed: %s", err)
		return nil, 0, err
	}
	repositories := resp.Payload
	repositoryResults := make([]*ccrmodel.RepositoryResult, 0)
	if len(repositories) == 0 {
		return repositoryResults, 0, nil
	}

	repositoryNames := make([]string, 0)
	for i := range repositories {
		repo := repositories[i]
		repositoryNames = append(repositoryNames, repo.Name)
	}
	repositoryNamesStr := strings.Join(repositoryNames, ",")
	ltp := tagapi.NewCountRepoTagsParamsWithContext(ctx).WithXRequestID(&requestId).
		WithProjectName(projectName).WithRepositoryNames(repositoryNamesStr)

	repoTags, err := harborClient.AddonClient.Tag.CountRepoTags(ltp, harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client call harbor failed: %s", err)
		return nil, 0, err
	}

	repoTagsMap := repoTags.Payload

	for i := range repositories {
		repository := repositories[i]

		data := strings.SplitN(repository.Name, "/", 2)
		repositoryResult := &ccrmodel.RepositoryResult{
			TagCount:       repoTagsMap[repository.Name],
			CreationTime:   repository.CreationTime,
			Description:    repository.Description,
			RepositoryName: data[1],
			UpdateTime:     repository.UpdateTime,
			ProjectName:    data[0],
			PullCount:      repository.PullCount,
		}

		repositoryResults = append(repositoryResults, repositoryResult)
	}

	return repositoryResults, resp.XTotalCount, nil
}

// BatchDeleteRepository Batch Delete the repository specified by name list
func (r *RepositoryService) BatchDeleteRepository(ctx context.Context, projectName string, repositoryNames []string) error {
	requestId := gin_context.RequestIdFromContext(ctx)
	logger := gin_context.LoggerFromContext(ctx)
	// 删除时使用模拟用户操作
	harborClient := middleware.HarborClientSimulateFromContext(ctx)

	// TODO use harbor-addon batch delete api
	for _, repositoryName := range repositoryNames {
		if repositoryName == "" {
			return errors.New(fmt.Sprintf("repository name:%s not provided", repositoryName))
		}
		exists, err := r.repositoryExists(ctx, projectName, repositoryName)
		if err != nil {
			return err
		}
		if !exists {
			logger.Warnf("repository: %s pair not found on server side", repositoryName)
			continue
		}
		_, err = harborClient.V2Client.Repository.DeleteRepository(
			repositoryapi.NewDeleteRepositoryParamsWithContext(ctx).WithProjectName(projectName).
				WithRepositoryName(repositoryName).WithXRequestID(&requestId), harborClient.AuthInfo)
		if err != nil {
			logger.Errorf("use swagger client call harbor failed: %s", err)
			return err
		}
	}

	return nil
}

// DeleteRepository Delete the repository specified by name
func (r *RepositoryService) DeleteRepository(ctx context.Context, projectName, repositoryName string) error {
	requestId := gin_context.RequestIdFromContext(ctx)
	logger := gin_context.LoggerFromContext(ctx)
	// 删除时使用模拟用户操作
	harborClient := middleware.HarborClientSimulateFromContext(ctx)

	_, err := harborClient.V2Client.Repository.DeleteRepository(
		repositoryapi.NewDeleteRepositoryParamsWithContext(ctx).WithProjectName(projectName).
			WithRepositoryName(repositoryName).WithXRequestID(&requestId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client call harbor failed: %s", err)
		return err
	}

	return nil
}

// GetRepository Get the repository specified by name
func (r *RepositoryService) GetRepository(ctx context.Context, projectName, repositoryName string, useSim bool) (*ccrmodel.RepositoryResult, error) {
	requestId := gin_context.RequestIdFromContext(ctx)
	logger := gin_context.LoggerFromContext(ctx)
	harborClient := middleware.HarborClientByUserFromContext(ctx, useSim)

	resp, err := harborClient.V2Client.Repository.GetRepository(
		repositoryapi.NewGetRepositoryParamsWithContext(ctx).WithProjectName(projectName).
			WithRepositoryName(repositoryName).WithXRequestID(&requestId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client call harbor failed: %s", err)
		return nil, err
	}
	repo := resp.Payload

	ltp := tagapi.NewCountTagsParamsWithContext(ctx).WithXRequestID(&requestId).
		WithProjectName(projectName).WithRepositoryName(repositoryName)

	repoTags, err := harborClient.AddonClient.Tag.CountTags(ltp, harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client call harbor failed: %s", err)
		return nil, err
	}

	tagCount := repoTags.Payload

	data := strings.SplitN(repo.Name, "/", 2)
	repositoryResult := &ccrmodel.RepositoryResult{
		TagCount:       tagCount,
		CreationTime:   repo.CreationTime,
		Description:    repo.Description,
		RepositoryName: data[1],
		UpdateTime:     repo.UpdateTime,
		ProjectName:    data[0],
		PullCount:      repo.PullCount,
	}

	return repositoryResult, nil
}

// UpdateRepository Update the repository specified by name
func (r *RepositoryService) UpdateRepository(ctx context.Context, projectName, repositoryName, description string, useSim bool) error {
	requestId := gin_context.RequestIdFromContext(ctx)
	logger := gin_context.LoggerFromContext(ctx)
	harborClient := middleware.HarborClientByUserFromContext(ctx, useSim)

	repositoryModel := &model.Repository{Description: description}
	_, err := harborClient.V2Client.Repository.UpdateRepository(
		repositoryapi.NewUpdateRepositoryParamsWithContext(ctx).WithProjectName(projectName).
			WithRepositoryName(repositoryName).WithRepository(repositoryModel).WithXRequestID(&requestId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client call harbor failed: %s", err)
		return err
	}

	return nil
}

// repositoryExists returns true, if p matches a repository on server side.
// Returns false, if not found.
// Returns an error in case of communication problems.
func (r *RepositoryService) repositoryExists(ctx context.Context, projectName, repositoryName string) (bool, error) {
	_, err := r.GetRepository(ctx, projectName, repositoryName, true)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (r *RepositoryService) ListAllRepositories(ctx context.Context, nameQ string, pageNo, pageSize int64) ([]*ccrmodel.RepositoryResult, int64, error) {
	requestId := gin_context.RequestIdFromContext(ctx)
	logger := gin_context.LoggerFromContext(ctx)
	harborClient := middleware.HarborClientFromContext(ctx)

	lrp := repositoryapi.NewListAllRepositoriesParamsWithContext(ctx).WithPage(&pageNo).WithPageSize(&pageSize).WithXRequestID(&requestId)

	if nameQ != "" {
		rn := fmt.Sprintf("name=~%s", nameQ)
		lrp.SetQ(&rn)
	}

	listResp, err := harborClient.V2Client.Repository.ListAllRepositories(
		lrp,
		harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client ListAllRepositories failed: %s", err)
		return nil, 0, err
	}

	projectIDs := make([]string, 0)
	for _, v := range listResp.Payload {
		projectIDs = append(projectIDs, fmt.Sprintf("%d", v.ProjectID))
	}
	projectsQuery := fmt.Sprintf("project_id={%s}", strings.Join(projectIDs, " "))
	projs, err := harborClient.V2Client.Project.ListProjects(
		project.NewListProjectsParamsWithContext(ctx).WithQ(&projectsQuery).WithPageSize(&pageSize),
		harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("use swagger client ListProjects failed: %s", err)
		return nil, 0, err
	}

	projectInfoMap := map[int64]*model.Project{}
	for _, v := range projs.Payload {
		projectInfoMap[int64(v.ProjectID)] = v
	}

	result := []*ccrmodel.RepositoryResult{}
	for _, v := range listResp.Payload {
		proj := projectInfoMap[v.ProjectID]
		if proj == nil {
			logger.Errorf("project %d not found", v.ProjectID)
			return nil, 0, fmt.Errorf("list with project not found")
		}

		publicStr := "unknown"
		if proj.Metadata != nil {
			publicStr = proj.Metadata.Public
		}

		result = append(result, &ccrmodel.RepositoryResult{
			// TODO: 暂时使用artifact count
			TagCount:       v.ArtifactCount,
			CreationTime:   v.CreationTime,
			Description:    v.Description,
			RepositoryName: strings.TrimPrefix(v.Name, proj.Name+"/"),
			ProjectName:    proj.Name,
			UpdateTime:     v.UpdateTime,
			Public:         publicStr,
		})
	}

	return result, listResp.XTotalCount, nil
}
