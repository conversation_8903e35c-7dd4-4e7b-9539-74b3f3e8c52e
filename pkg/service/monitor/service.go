package monitor

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/jinzhu/copier"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/yaml"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/api/v1/cnap"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/aigateway"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	monitorModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

const (
	EnvoyJobName = "envoy-stats"
	IstioJobName = "istiod"
)

type Service struct {
	opt            *Option
	cceService     cce.ClientInterface
	MonitorModel   monitorModel.ServiceInterface
	InstanceModel  instances.ServiceInterface
	ClusterModel   cluster.ServiceInterface
	AIGatewayModel aigateway.ServiceInterface
}

func NewMonitorService(option *Option) *Service {
	gormDB := option.DB.DB
	svc := &Service{
		opt:            option,
		cceService:     cce.NewClientService(),
		MonitorModel:   monitorModel.NewService(monitorModel.NewOption()),
		InstanceModel:  instances.NewInstancesService(instances.NewOption(gormDB)),
		ClusterModel:   cluster.NewClusterService(cluster.NewOption(gormDB)),
		AIGatewayModel: aigateway.NewAIGatewayService(aigateway.NewOption(gormDB)),
	}
	return svc
}

func (service *Service) ClusterCPromAgentCheck(ctx csmContext.CsmContext, region, cpromInstanceID,
	clusterID string) *vo.CPromAgentCheckResult {
	res := &vo.CPromAgentCheckResult{
		IsExit: false,
	}
	agent, err := service.MonitorModel.GetCPromAgent(ctx, cpromInstanceID, region, clusterID)
	if err == nil && agent != nil && agent.AgentID != "" {
		res.IsExit = true
	}
	return res
}

// GetMonitorInstancesByHosting 获取可以安装托管网格控制面采集任务的CProm实例列表
// 处理逻辑：
// 1、获取所有的CProm实例。
// 2、判断clusterID是否为空。
//   - 为空：则查找所有接入了与csmInstance.vpcID相同vpc的CProm实例，并将 IsInstallCPromAgent 字段置为true，其余实例置为false。
//   - 【目前用不到，为了支持后续拓展】不为空：则查找所有ClusterID对应cce集群安装了agent的CProm实例，
//     并将 IsInstallCPromAgent 字段置为true， 其余实例置为false。
func (service *Service) GetMonitorInstancesByHosting(ctx csmContext.CsmContext,
	clusterID, instanceId, region string) (mis *vo.MonitorInstances, err error) {
	monitorInstances := make([]*vo.MonitorInstance, 0)
	idRegionMap := make(map[string]struct{})
	csmInstance, err := service.InstanceModel.GetInstanceByInstanceUUID(ctx, instanceId)
	if err != nil {
		return nil, err
	}
	// 非托管的网格实例直接报错
	if csmInstance == nil || !strings.EqualFold(csmInstance.InstanceType, string(cnap.TypeHosting)) {
		return nil, csmErr.NewInvalidParameterValueException(
			fmt.Sprintf("instance is not hosting that instanceId %s", instanceId))
	}

	// 校验：当clusterID和InstanceID都传时，需要判断clusterID是否为remote集群。目前前端暂无该用法
	if len(clusterID) > 0 {
		remoteClusters, err := service.ClusterModel.GetAllRemoteClusterByInstanceUUID(ctx, instanceId)
		if err != nil {
			return nil, csmErr.NewInvalidParameterValueException(
				fmt.Sprintf("instanceId %s get remote cluster failed", instanceId))
		}
		isRemote := false
		for _, c := range *remoteClusters {
			if c.ClusterUUID == clusterID {
				isRemote = true
				break
			}
		}
		if !isRemote {
			return nil, csmErr.NewInvalidParameterValueException(
				fmt.Sprintf("clusterID %s is not remote cluster could not install monitor", clusterID))
		}
	}

	// 获取所有CProm实例
	cpis, err := service.MonitorModel.GetCPromInstancesByRegion(ctx, region)
	if err != nil {
		ctx.CsmLogger().Errorf("Get CProm instances error. %v", err)
	}

	// 获取当前CProm已经接入的
	for _, i := range cpis {
		// 兼容 CProm 实例没有 region的情况，使用 region 字段填充
		cPromRegion := i.Region
		if cPromRegion == "" {
			cPromRegion = region
		}
		monitorInstance := &vo.MonitorInstance{
			Region: cPromRegion,
			ID:     i.InstanceId,
			Name:   i.InstanceName,
		}
		if _, ok := idRegionMap[i.InstanceId+"-"+cPromRegion]; !ok {
			monitorInstances = append(monitorInstances, monitorInstance)
			idRegionMap[i.InstanceId+"-"+cPromRegion] = struct{}{}
		}
	}
	// 查询 CProm 实例关联的 CCE 集群（clusterID 是否安装 agent）
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 遍历服务实例列表，对于每个实例，筛选出主集群并查询连接的sidecar数量
	for _, instance := range monitorInstances {
		wg.Add(1)
		go func(instance *vo.MonitorInstance) {
			cpromInstanceID := instance.ID
			region := instance.Region

			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("GetCpromAgentList with id:%s region:%s error %v",
						cpromInstanceID, region, e)
				}
				wg.Done()
			}()

			cpromAgentList, err := service.MonitorModel.GetCpromAgentList(ctx, cpromInstanceID, region)
			if err != nil {
				ctx.CsmLogger().Errorf("GetCPromAgentList with id:%s region:%s error %v",
					cpromInstanceID, region, err)
			}
			if cpromAgentList != nil && len(cpromAgentList.Items) > 0 {
				for _, agent := range cpromAgentList.Items {
					if len(agent.AgentID) == 0 {
						continue
					}
					bindingCluster := agent.Cluster
					if bindingCluster.Spec != nil && bindingCluster.Spec.VpcID == csmInstance.VpcNetworkId {
						// 如果clusterID不为空，则必须clusterID相同，才能将IsInstallCPromAgent置为true
						if len(clusterID) > 0 && bindingCluster.Spec.ClusterID != clusterID {
							continue
						}
						mu.Lock()
						instance.IsInstallCPromAgent = true
						mu.Unlock()
					}
				}
			}

		}(instance)
	}
	wg.Wait()
	return &vo.MonitorInstances{Instances: monitorInstances}, nil
}

// GetInstanceTokenList 获取监控实例访问token
func (service *Service) GetInstanceTokenList(ctx csmContext.CsmContext, instanceId string) (*meta.CPromInstanceTokens, error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	return service.MonitorModel.GetInstanceTokenList(ctx, region, instanceId)
}

// CreateInstanceToken 创建监控实例访问token
func (service *Service) CreateInstanceToken(ctx csmContext.CsmContext,
	instanceId string) (token string, err error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	return service.MonitorModel.CreateInstanceToken(ctx, region, instanceId)
}

// GetCpromInstances 获取指定region下的所有监控实例
func (service *Service) GetCpromInstances(ctx csmContext.CsmContext) ([]meta.CPromInstance, error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	return service.MonitorModel.GetCPromInstancesByRegion(ctx, region)
}

// GetCpromInstanceDetail 获取指定CProm实例的详细信息
func (service *Service) GetCpromInstanceDetail(ctx csmContext.CsmContext, instanceId string) (*meta.CPromItem, error) {
	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	return service.MonitorModel.GetCPromInstanceDetail(ctx, region, instanceId)
}
func (service *Service) GetMonitorInstances(ctx csmContext.CsmContext,
	clusterID, instanceId string) (mis *vo.MonitorInstances, err error) {
	monitorInstances := make([]*vo.MonitorInstance, 0)
	idRegionMap := make(map[string]struct{})

	region := ctx.Request().Header.Get(constants.RegionHeaderKey)
	// 兼容托管网格开启控制面监控逻辑
	if instanceId != "" {
		return service.GetMonitorInstancesByHosting(ctx, clusterID, instanceId, region)
	}

	// 获取 agent 实例列表
	cpis, err := service.MonitorModel.GetCPromInstancesByRegion(ctx, region)
	if err != nil {
		ctx.CsmLogger().Errorf("Get CProm instances error. %v", err)
	} else {
		for _, i := range cpis {
			// 兼容 CProm 实例没有 region，使用 region 字段填充
			cPromRegion := i.Region
			if cPromRegion == "" {
				cPromRegion = region
			}
			monitorInstance := &vo.MonitorInstance{
				Region: cPromRegion,
				ID:     i.InstanceId,
				Name:   i.InstanceName,
			}
			if _, ok := idRegionMap[i.InstanceId+"-"+cPromRegion]; !ok {
				monitorInstances = append(monitorInstances, monitorInstance)
				idRegionMap[i.InstanceId+"-"+cPromRegion] = struct{}{}
			}
		}
	}
	// 查询 CProm 实例关联的 CCE 集群（clusterID 是否安装 agent）
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 遍历服务实例列表，对于每个实例，筛选出主集群并查询连接的sidecar数量
	for _, instance := range monitorInstances {
		wg.Add(1)
		go func(instance *vo.MonitorInstance) {
			cpromInstanceID := instance.ID
			region := instance.Region

			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("GetCpromAgentList with id:%s region:%s error %v",
						cpromInstanceID, region, e)
				}
				wg.Done()
			}()

			cpromAgentList, err := service.MonitorModel.GetCpromAgentList(ctx, cpromInstanceID, region)
			if err != nil {
				ctx.CsmLogger().Errorf("GetCPromAgentList with id:%s region:%s error %v",
					cpromInstanceID, region, err)
			}
			if len(clusterID) > 0 {
				if cpromAgentList != nil && len(cpromAgentList.Items) > 0 {
					for _, agent := range cpromAgentList.Items {
						if len(agent.AgentID) == 0 {
							continue
						}
						cluster := agent.Cluster
						if cluster.Spec != nil && cluster.Spec.ClusterID == clusterID {
							mu.Lock()
							instance.IsInstallCPromAgent = true
							mu.Unlock()
						}
					}
				}
			}

		}(instance)
	}
	wg.Wait()
	return &vo.MonitorInstances{Instances: monitorInstances}, nil
}

// getMonitorInstanceDetailByHosting 托管网格获取CProm实例详情，目前托管网格仅开通控制面监控
// 处理逻辑
// 1、从数据库cluster表中获取托管网格控制面监控相关的CProm监控信息
// 2、根据信息调用CProm接口检查状态
// - 检查agent状态，是否运行正常，不正常提示用户去查看
// - 检查采集任务状态，是否存在，被删除时提醒用户去查看
func (service *Service) getMonitorInstanceDetailByHosting(ctx csmContext.CsmContext,
	csmInstance *meta.Instances) (*vo.MonitorInstanceDetail, error) {
	monitorInstanceDetail := &vo.MonitorInstanceDetail{
		CSMMonitor: &vo.CSMMonitor{
			Enabled:       true,
			MonitorDetail: false,
		},
	}

	// 查询external集群
	externalCluster, _, err := service.InstanceModel.GetInstanceIstiodCluster(ctx, csmInstance.InstanceUUID)
	if err != nil {
		return monitorInstanceDetail, err
	}

	monitorRegion := externalCluster.MonitorRegion
	monitorInstanceId := externalCluster.MonitorInstanceId
	// 判断 CProm 实例状态(被删除或者其他)
	item, err := service.MonitorModel.GetCPromInstanceDetail(ctx, monitorRegion, monitorInstanceId)
	if item != nil && item.Spec == nil || err != nil {
		monitorInstanceDetail.CSMMonitor.MonitorReason = vo.CPromInstanceDeletedException
		monitorInstanceDetail.CSMMonitor.Description = "CProm instance is deleted, please choose other CProm instance"
		return monitorInstanceDetail, nil
	}

	err = copier.Copy(monitorInstanceDetail, item)
	if err != nil {
		return monitorInstanceDetail, err
	}

	// 网格实例关联的集群 对应的 Agent 是否正常
	allCPromAgents, getErr := service.MonitorModel.GetCpromAgentList(ctx, monitorInstanceId, monitorRegion)
	if getErr != nil {
		return monitorInstanceDetail, getErr
	}

	agentStatus := constants.CsmCPromAgentStatusUnknown
	agentID := externalCluster.MonitorAgentID
	if allCPromAgents != nil && len(allCPromAgents.Items) > 0 {
		for _, agent := range allCPromAgents.Items {
			cluster := agent.Cluster
			if cluster == nil || cluster.Spec == nil {
				continue
			}
			if agent.AgentID == externalCluster.MonitorAgentID {
				agentStatus = agent.AgentStatus
			}
		}
	}

	if agentStatus != constants.CsmCPromAgentStatusRunning {
		monitorInstanceDetail.CSMMonitor.MonitorReason = vo.CPromAgentException
		monitorInstanceDetail.CSMMonitor.Description = "Agent exception, please check it"
		return monitorInstanceDetail, nil
	}

	// 查询网格实例对应的 job 是否正常
	allJobs, err := service.MonitorModel.GetAllScrapeJobs(ctx, monitorRegion, monitorInstanceId, agentID)
	if err != nil {
		return monitorInstanceDetail, err
	}
	jobStatus := false
	if allJobs != nil && len(allJobs.Items) > 0 {
		for _, job := range allJobs.Items {
			// 托管网格监控特有，externalCluster.MonitorJobIds 中仅有一个jobID，可以直接比较。
			if job.Spec != nil && strings.EqualFold(job.Spec.ScrapeJobID, externalCluster.MonitorJobIds) {
				jobStatus = true
				break
			}
		}
	}
	if !jobStatus {
		monitorInstanceDetail.CSMMonitor.MonitorReason = vo.CSMEnvoyJobDeletedException
		monitorInstanceDetail.CSMMonitor.Description = fmt.Sprintf("%s job is deleted, please check it", externalCluster.MonitorJobIds)
		return monitorInstanceDetail, nil
	}

	// 正常情况
	monitorInstanceDetail.CSMMonitor.MonitorDetail = true

	return monitorInstanceDetail, nil
}

// 网格实例对应的监控实例详细信息
// TODO 需要新增独立网格控制面监控信息分析
func (service *Service) GetMonitorInstanceDetail(ctx csmContext.CsmContext,
	instanceID string) (*vo.MonitorInstanceDetail, error) {
	monitorInstanceDetail := &vo.MonitorInstanceDetail{
		CSMMonitor: &vo.CSMMonitor{
			Enabled:       false,
			MonitorDetail: false,
		},
	}

	instance, err := service.InstanceModel.GetInstanceByInstanceUUID(ctx, instanceID)
	if err != nil {
		return monitorInstanceDetail, err
	}

	if instance == nil {
		return monitorInstanceDetail, csmErr.NewInvalidParameterValueException(fmt.Sprintf(
			"invalid instanceId %s", instanceID))
	}
	// 未开启监控
	monitorEnabled := *instance.MonitorEnabled
	if !monitorEnabled {
		return monitorInstanceDetail, nil
	}

	// 托管网格处理逻辑
	if strings.EqualFold(instance.InstanceType, string(meta.HostingMeshType)) {
		return service.getMonitorInstanceDetailByHosting(ctx, instance)
	}

	// 实例开启了 CProm 监控
	monitorInstanceDetail.CSMMonitor.Enabled = true

	var allClusters []string

	// 查询 CProm 实例关联的 CCE 集群
	// 查询主集群
	primaryCluster, _, err := service.InstanceModel.GetInstanceIstiodCluster(ctx, instanceID)
	if err != nil {
		return monitorInstanceDetail, err
	}

	monitorRegion := primaryCluster.MonitorRegion
	monitorInstanceId := primaryCluster.MonitorInstanceId
	// 判断 CProm 实例状态(被删除或者其他)
	item, err := service.MonitorModel.GetCPromInstanceDetail(ctx, monitorRegion, monitorInstanceId)
	if item != nil && item.Spec == nil || err != nil {
		monitorInstanceDetail.CSMMonitor.MonitorReason = vo.CPromInstanceDeletedException
		monitorInstanceDetail.CSMMonitor.Description = "CProm instance is deleted, please choose other CProm instance"
		return monitorInstanceDetail, nil
	}

	allClusters = append(allClusters, primaryCluster.ClusterUUID)
	// 查询从集群
	remoteClusters, err := service.ClusterModel.GetAllRemoteClusterByInstanceUUID(ctx, instanceID)
	if err != nil {
		return monitorInstanceDetail, err
	}
	if remoteClusters != nil {
		for _, remoteCluster := range *remoteClusters {
			allClusters = append(allClusters, remoteCluster.ClusterUUID)
		}
	}

	err = copier.Copy(monitorInstanceDetail, item)
	if err != nil {
		return monitorInstanceDetail, err
	}

	// 网格实例关联的集群（primary + remote）对应的 Agent 是否正常
	allCPromAgents, getErr := service.MonitorModel.GetCpromAgentList(ctx, monitorInstanceId, monitorRegion)
	if getErr != nil {
		return monitorInstanceDetail, getErr
	}

	agentStatus := true
	agentTotal := 0
	agentID := ""
	if allCPromAgents != nil && len(allCPromAgents.Items) > 0 {
		for _, agent := range allCPromAgents.Items {
			cluster := agent.Cluster
			if cluster == nil || cluster.Spec == nil {
				continue
			}
			if agent.AgentID != "" || agent.AgentStatus != "" {
				if stringInSlice(cluster.Spec.ClusterID, allClusters) {
					agentTotal++
					agentID = agent.AgentID
				}
			}
		}
	}

	// cce 关联的集群，Agent 状态异常
	if agentID == "" || agentTotal != len(allClusters) {
		agentStatus = false
	}
	if !agentStatus {
		monitorInstanceDetail.CSMMonitor.MonitorReason = vo.CPromAgentException
		monitorInstanceDetail.CSMMonitor.Description = "Agent exception, please check it"
		return monitorInstanceDetail, nil
	}

	// 查询网格实例对应的 cce 集群（primary + remote）中的 job 是否正常
	allJobs, err := service.MonitorModel.GetAllScrapeJobs(ctx, monitorRegion, monitorInstanceId, agentID)
	if err != nil {
		return monitorInstanceDetail, err
	}
	jobStatus := false
	if allJobs != nil && len(allJobs.Items) > 0 {
		for _, job := range allJobs.Items {
			spec := job.Spec
			if strings.EqualFold(spec.ScrapeJobName, EnvoyJobName) {
				jobStatus = true
				break
			}
		}
	}
	if !jobStatus {
		monitorInstanceDetail.CSMMonitor.MonitorReason = vo.CSMEnvoyJobDeletedException
		monitorInstanceDetail.CSMMonitor.Description = fmt.Sprintf("%s job is deleted, please check it", EnvoyJobName)
		return monitorInstanceDetail, nil
	}

	// 正常情况
	monitorInstanceDetail.CSMMonitor.MonitorDetail = true

	return monitorInstanceDetail, nil
}

func stringInSlice(str string, list []string) bool {
	for _, v := range list {
		if v == str {
			return true
		}
	}
	return false
}

// 更新从集群监控状态
func (service *Service) UpdateRemoteMonitor(ctx csmContext.CsmContext, instanceID,
	clusterID, removeJobId string, monitorInstance *vo.MonitorInstances) error {
	// 关 -> 开
	if monitorInstance.Enabled {
		ctx.CsmLogger().Infof("UpdateRemoteMonitor close to open")
		monitorInstanceID := monitorInstance.Instances[0].ID
		region := monitorInstance.Instances[0].Region
		// 添加 job
		agent, err := service.MonitorModel.GetCPromAgent(ctx, monitorInstanceID, region, clusterID)
		if err != nil {
			ctx.CsmLogger().Errorf("UpdateRemoteMonitor GetCPromAgent error %v", err)
			return err
		}
		jobIds := make([]string, 0)
		// TODO 暂时不支持采集 Istiod 指标，后续支持
		// istioJobID, err := service.MonitorModel.CreateIstioScrapeJob(ctx, update.Instances[0].Region, update.Instances[0].ID, agent.AgentID)
		// if err != nil {
		// 	return nil, err
		// }
		// jobIds = append(jobIds, istioJobID)
		envoyJobID, err := service.MonitorModel.CreateEnvoyScrapeJob(ctx, region, monitorInstanceID, agent.AgentID)
		if err != nil {
			ctx.CsmLogger().Errorf("UpdateRemoteMonitor CreateEnvoyScrapeJob error %v", err)
			return err
		}
		ctx.CsmLogger().Infof("create scrape job id:%s, cprom instance id:%s, agent id:%s successful",
			envoyJobID, monitorInstanceID, agent.AgentID)
		jobIds = append(jobIds, envoyJobID)

		// 更新 remote cluster
		pc := &meta.Cluster{
			MonitorJobIds:     strings.Join(jobIds, ","),
			MonitorInstanceId: monitorInstanceID,
			MonitorRegion:     region,
			ClusterUUID:       clusterID,
		}
		// 更新从集群监控状态
		err = service.ClusterModel.UpdateCluster(ctx, instanceID, pc.ClusterUUID, pc.Region, pc)
		if err != nil {
			ctx.CsmLogger().Errorf("UpdateRemoteMonitor updateCluster error %v", err)
			return err
		}
		ctx.CsmLogger().Infof("end UpdateRemoteMonitor successful")
	} else {
		ctx.CsmLogger().Infof("UpdateRemoteMonitor open to close with jobIDs=%s", removeJobId)
		// 开 -> 关
		cpromInstanceID := monitorInstance.Instances[0].ID
		region := monitorInstance.Instances[0].Region
		if len(removeJobId) > 0 {
			// 删除原有 job
			agent, err := service.MonitorModel.GetCPromAgent(ctx, cpromInstanceID, region, clusterID)
			if err != nil {
				ctx.CsmLogger().Warnf("get cprom agent failed. cprom instance id:%s , region:%s , cluster id:%s",
					cpromInstanceID, region, clusterID)
			} else {
				for _, jobId := range strings.Split(removeJobId, ",") {
					err = service.MonitorModel.DeleteScrapeJob(ctx, region, jobId, cpromInstanceID, agent.AgentID)
					if err != nil {
						// 当删除采集任务失败时，不返回错误，用户依然可以在 crpom 产品页面进行手动删除
						ctx.CsmLogger().Warnf("delete scrape job failed. job id:%s , cprom instance id:%s , agent id:%s",
							jobId, cpromInstanceID, agent.AgentID)
						continue
					}
					ctx.CsmLogger().Infof("delete scrape job id:%s, cprom instance id:%s, agent id:%s successful",
						jobId, cpromInstanceID, agent.AgentID)
				}
			}
		}
		// 更新 remote cluster
		pc := &meta.Cluster{
			MonitorJobIds:     "",
			MonitorInstanceId: "",
			MonitorRegion:     "",
		}
		updateErr := service.ClusterModel.UpdateCluster(ctx, instanceID, clusterID, region, pc)
		if updateErr != nil {
			ctx.CsmLogger().Errorf("updateCluster error %v", updateErr)
		}
	}
	return nil
}

// nolint:gocyclo
// updateMonitorByHosting 托管网格变更控制面监控
// 处理逻辑：
// 1、根据CProm实例ID，获取CProm实例下所有agent
// 2、获取托管网格的所有remote集群，优先在安装了CProm agent的remote集群中创建控制面采集任务。
//   - 如果存在安装了CProm agent的remote集群，则根据remote集群被纳管的时间顺序，将采集任务部署在最早纳管的remote集群的agent上。
//   - 如果不存在安装了CProm agent的remote集群或者没有remote集群，则随机选取一个agent部署采集任务。
//
// 3、将agentID，jobID等信息保存在托管网格的external cluster集群数据库表中的 monitor_* 相关字段。
func (service *Service) updateMonitorByHosting(ctx csmContext.CsmContext, csmInstance *meta.Instances,
	update *vo.MonitorInstances, instanceModel instances.ServiceInterface,
	clusterModel cluster.ServiceInterface) (updated *vo.MonitorInstances, err error) {
	if csmInstance == nil {
		return nil, csmErr.NewInvalidParameterValueException("csmInstance is nil")
	}
	// 获取external集群，将CProm相关信息更新在external集群monitor_*字段上
	externalCluster, err := clusterModel.GetIstiodCluster(ctx, csmInstance.InstanceUUID, csmInstance.InstanceType)
	if err != nil {
		return nil, err
	}
	// 获取所有remote集群
	remoteClusters, err := clusterModel.GetAllRemoteClusterByInstanceUUID(ctx, csmInstance.InstanceUUID)
	if err != nil {
		return nil, err
	}
	// 按照纳管时间排序，纳管早的排在最前面
	if remoteClusters != nil && len(*remoteClusters) > 0 {
		sort.Slice(*remoteClusters, func(i, j int) bool {
			return (*remoteClusters)[i].CreateTime.Before(*(*remoteClusters)[j].CreateTime)
		})
	}

	newInstance := &meta.Instances{}
	err = copier.Copy(newInstance, csmInstance)
	if err != nil {
		return nil, err
	}

	// 目前托管网格的MonitorEnabled字段，仅表示是否开启控制面监控。
	// 关 -> 开
	if !(*csmInstance.MonitorEnabled) && update.Enabled {
		if len(update.Instances) == 0 {
			return nil, fmt.Errorf("no monitor instances in %v", update)
		}
		// 获取所有agent
		cpromInstanceID := update.Instances[0].ID
		region := update.Instances[0].Region
		agentID := ""
		agents, agentErr := service.MonitorModel.GetCpromAgentList(ctx, cpromInstanceID, region)
		if agentErr != nil {
			return nil, agentErr
		}

		// remote集群不为空，则先找remote集群中的agent
		if remoteClusters != nil && len(*remoteClusters) > 0 {
		outerLoop:
			for _, remoteCluster := range *remoteClusters {
				for _, i := range agents.Items {
					if i.Cluster == nil || i.Cluster.Spec == nil {
						continue
					}
					if i.Cluster.Spec.ClusterID == remoteCluster.ClusterUUID &&
						i.Cluster.Spec.VpcID == csmInstance.VpcNetworkId {
						agentID = i.AgentID
						break outerLoop
					}
				}
			}
		}
		// 如果agentID还为空，则说明没有纳管的集群，则随机选择一个vpc相同的agent。
		if agentID == "" {
			for _, i := range agents.Items {
				if i.Cluster == nil || i.Cluster.Spec == nil {
					continue
				}
				if i.Cluster.Spec.VpcID == csmInstance.VpcNetworkId {
					agentID = i.AgentID
					break
				}
			}
		}
		//创建采集任务
		istioJobID, createErr := service.MonitorModel.CreateHostingIstioScrapeJob(ctx, csmInstance.InstanceUUID,
			region, cpromInstanceID, agentID)
		if createErr != nil {
			return nil, createErr
		}

		// 更新控制面监控信息在external集群字段上。
		externalCluster.MonitorJobIds = istioJobID
		externalCluster.MonitorInstanceId = cpromInstanceID
		externalCluster.MonitorRegion = region
		externalCluster.MonitorAgentID = agentID
		updateErr := clusterModel.UpdateCluster(ctx, externalCluster.InstanceUUID, externalCluster.ClusterUUID,
			externalCluster.Region, externalCluster)
		if updateErr != nil {
			ctx.CsmLogger().Errorf("updateCluster error %s", updateErr.Error())
			// 失败，删除采集任务
			_ = service.MonitorModel.DeleteScrapeJob(ctx, region, istioJobID, cpromInstanceID, agentID)
			return nil, updateErr
		}

		// 更新 csmInstance
		newInstance.MonitorEnabled = csm.Bool(update.Enabled)
		_, updateInsErr := instanceModel.UpdateInstance(ctx, csmInstance, newInstance)
		if updateInsErr != nil {
			return nil, updateInsErr
		}

		// 更新istiod的annotation
		kubeClient, clientErr := service.cceService.NewClient(ctx, externalCluster.Region, externalCluster.ClusterUUID,
			meta.MeshType(csmInstance.InstanceType))
		if clientErr != nil {
			return nil, clientErr
		}
		istioSvc, getErr := kubeClient.Kube().CoreV1().Services(csmInstance.IstioInstallNamespace).Get(context.TODO(),
			constants.IstiodServiceName, metav1.GetOptions{})
		if getErr != nil {
			// 失败，删除采集任务
			_ = service.MonitorModel.DeleteScrapeJob(ctx, region, istioJobID, cpromInstanceID, agentID)
			return nil, getErr
		}
		// update monitor info
		istioSvc.Annotations[constants.CsmCPromInstanceID] = cpromInstanceID
		istioSvc.Annotations[constants.CsmCPromAgentID] = agentID
		istioSvc.Annotations[constants.CsmCPromScrapeJobID] = istioJobID
		istioSvc.Annotations[constants.CSMHostingMonitor] = constants.CSMHostingMonitorIstioType
		istioSvc.Annotations[constants.CSMHostingBlbAccountID] = csmInstance.AccountId

		_, updateCrdErr := kubeClient.Kube().CoreV1().Services(csmInstance.IstioInstallNamespace).Update(context.TODO(),
			istioSvc, metav1.UpdateOptions{})
		if updateCrdErr != nil {
			ctx.CsmLogger().Errorf("failed update istiod cprom annotation, err is %s", updateCrdErr.Error())
			// 失败，删除采集任务
			_ = service.MonitorModel.DeleteScrapeJob(ctx, region, istioJobID, cpromInstanceID, agentID)
			return nil, updateCrdErr
		}
	}

	// 开 -> 关
	if *(csmInstance.MonitorEnabled) && !(update.Enabled) {
		// 清除 job
		if len(externalCluster.MonitorJobIds) != 0 && len(externalCluster.MonitorInstanceId) != 0 {
			// 删除原有 job
			deleteErr := service.MonitorModel.DeleteScrapeJob(ctx, externalCluster.MonitorRegion,
				externalCluster.MonitorJobIds, externalCluster.MonitorInstanceId, externalCluster.MonitorAgentID)
			if deleteErr != nil && !constants.IsAcceptExceptionForCProm(deleteErr) {
				ctx.CsmLogger().Warnf("delete scrape job failed. job id : %s , cprom instance id : %s , agent id : %s",
					externalCluster.MonitorJobIds, externalCluster, externalCluster.MonitorAgentID)
				return nil, deleteErr
			}
		}
		// 更新 csmInstance
		newInstance.MonitorEnabled = csm.Bool(update.Enabled)
		_, updateInsErr := instanceModel.UpdateInstance(ctx, csmInstance, newInstance)
		if updateInsErr != nil {
			return nil, updateInsErr
		}

		// 更新 external cluster
		externalCluster.MonitorJobIds = ""
		externalCluster.MonitorInstanceId = ""
		externalCluster.MonitorRegion = ""
		externalCluster.MonitorAgentID = ""
		updateErr := clusterModel.UpdateCluster(ctx, externalCluster.InstanceUUID, externalCluster.ClusterUUID,
			externalCluster.Region, externalCluster)
		if updateErr != nil {
			return nil, updateErr
		}

		// 更新istiod的annotation
		kubeClient, clientErr := service.cceService.NewClient(ctx, externalCluster.Region, externalCluster.ClusterUUID,
			meta.MeshType(csmInstance.InstanceType))
		if clientErr != nil {
			return nil, clientErr
		}
		istioSvc, getErr := kubeClient.Kube().CoreV1().Services(csmInstance.IstioInstallNamespace).Get(context.TODO(),
			constants.IstiodServiceName, metav1.GetOptions{})
		if getErr != nil {
			return nil, getErr
		}
		// update monitor info
		istioSvc.Annotations[constants.CsmCPromInstanceID] = ""
		istioSvc.Annotations[constants.CsmCPromAgentID] = ""
		istioSvc.Annotations[constants.CsmCPromScrapeJobID] = ""
		istioSvc.Annotations[constants.CSMHostingMonitor] = ""
		istioSvc.Annotations[constants.CSMHostingBlbAccountID] = ""

		_, updateCrdErr := kubeClient.Kube().CoreV1().Services(csmInstance.IstioInstallNamespace).Update(context.TODO(),
			istioSvc, metav1.UpdateOptions{})
		if updateCrdErr != nil {
			ctx.CsmLogger().Errorf("failed update istiod cprom annotation, err is %s", updateCrdErr.Error())
			return nil, updateCrdErr
		}
	}
	return update, nil
}

// nolint:gocyclo
// Cyclomatic complexity, Current function complexity is high detail: http://stylecheck.baidu.com/stylerule/go/GoRule212
// TODO 降低函数复杂度
func (service *Service) UpdateMonitor(ctx csmContext.CsmContext, instanceID string, update *vo.MonitorInstances) (
	updated *vo.MonitorInstances, err error) {

	tx := service.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	instanceModel := service.InstanceModel.WithTx(dbutil.NewDB(tx))
	clusterModel := service.ClusterModel.WithTx(dbutil.NewDB(tx))

	instance, err := instanceModel.GetInstanceByInstanceUUID(ctx, instanceID)
	if err != nil {
		return nil, err
	}

	if instance == nil {
		return nil, csmErr.NewInvalidParameterValueException(
			fmt.Sprintf("invalid instanceID %s, instance is empty", instanceID))
	}

	// 兼容托管网格监控
	if strings.EqualFold(instance.InstanceType, string(meta.HostingMeshType)) {
		updated, err = service.updateMonitorByHosting(ctx, instance, update, instanceModel, clusterModel)
		if err != nil {
			return nil, err
		}
		tx.Commit()
		return updated, nil
	}

	pc, _, err := instanceModel.GetInstanceIstiodCluster(ctx, instanceID)
	if err != nil {
		return nil, err
	}

	// 开 -> 关
	if *(instance.MonitorEnabled) && !(update.Enabled) {
		// 清除 job
		if len(pc.MonitorJobIds) != 0 && len(pc.MonitorInstanceId) != 0 {
			// 删除原有 job
			agent, err := service.MonitorModel.GetCPromAgent(ctx, pc.MonitorInstanceId, pc.Region, pc.ClusterUUID)
			if err != nil {
				ctx.CsmLogger().Warnf("get cprom agent failed. cprom instance id : %s , region : %s , cluster id : %s",
					pc.MonitorInstanceId, pc.Region, pc.ClusterUUID)
			} else {
				for _, jobId := range strings.Split(pc.MonitorJobIds, ",") {
					err = service.MonitorModel.DeleteScrapeJob(ctx, pc.Region, jobId, pc.MonitorInstanceId, agent.AgentID)
					if err != nil {
						// 当删除采集任务失败时，不返回错误，用户依然可以在 crpom 产品页面进行手动删除
						// TODO: 后续建立 watch 机制，定时同步状态
						ctx.CsmLogger().Warnf("delete scrape job failed. job id : %s , cprom instance id : %s , agent id : %s",
							jobId, pc.MonitorInstanceId, agent.AgentID)
						continue
					}
				}
			}
		}
		// 更新 instance 和 primary cluster
		newInstance := &meta.Instances{}
		err := copier.Copy(newInstance, instance)
		if err != nil {
			return nil, err
		}
		newInstance.MonitorEnabled = csm.Bool(update.Enabled)
		_, updateInsErr := instanceModel.UpdateInstance(ctx, instance, newInstance)
		if updateInsErr != nil {
			ctx.CsmLogger().Error("UpdateInstance failed")
		}
		pc.MonitorJobIds = ""
		pc.MonitorInstanceId = ""
		pc.MonitorRegion = ""
		updateErr := clusterModel.UpdateCluster(ctx, instanceID, pc.ClusterUUID, pc.Region, pc)
		if updateErr != nil {
			ctx.CsmLogger().Errorf("updateCluster error %v", updateErr)
		}
	}

	// 关 -> 开
	if !(*instance.MonitorEnabled) && update.Enabled {
		if len(update.Instances) == 0 {
			return nil, fmt.Errorf("no monitor instances in %v", update)
		}
		// 添加 job
		cpromInstanceID := update.Instances[0].ID
		region := update.Instances[0].Region
		agent, err := service.MonitorModel.GetCPromAgent(ctx, cpromInstanceID, region, pc.ClusterUUID)
		if err != nil {
			return nil, err
		}

		jobIds := make([]string, 0)
		// 支持采集 istiod 指标，后续支持
		istioJobID, err := service.MonitorModel.CreateIstioScrapeJob(ctx, update.Instances[0].Region, update.Instances[0].ID, agent.AgentID)
		if err != nil {
			return nil, err
		}
		jobIds = append(jobIds, istioJobID)

		envoyJobID, err := service.MonitorModel.CreateEnvoyScrapeJob(ctx, update.Instances[0].Region, update.Instances[0].ID, agent.AgentID)
		if err != nil {
			return nil, err
		}
		jobIds = append(jobIds, envoyJobID)

		// 更新 instance 和 primary cluster
		newInstance := &meta.Instances{}
		err = copier.Copy(newInstance, instance)
		if err != nil {
			return nil, err
		}
		newInstance.MonitorEnabled = csm.Bool(update.Enabled)
		_, updateInsErr := instanceModel.UpdateInstance(ctx, instance, newInstance)
		if updateInsErr != nil {
			ctx.CsmLogger().Error("UpdateInstance failed")
		}

		pc.MonitorJobIds = strings.Join(jobIds, ",")
		pc.MonitorInstanceId = update.Instances[0].ID
		pc.MonitorRegion = update.Instances[0].Region

		updateErr := clusterModel.UpdateCluster(ctx, instanceID, pc.ClusterUUID, pc.Region, pc)
		if updateErr != nil {
			ctx.CsmLogger().Errorf("updateCluster error %v", updateErr)
		}
	}
	tx.Commit()

	return update, nil
}

func (service *Service) CreateGatewayMonitor(ctx csmContext.CsmContext, csmInstanceID, vpcID string,
	cpromInstance *meta.CPromInstance) (*meta.CPromGatewayInfo, error) {
	if vpcID == "" {
		return nil, csmErr.NewInvalidParameterValueException("vpcID is empty")
	}
	if cpromInstance == nil || cpromInstance.InstanceId == "" || cpromInstance.Region == "" {
		return nil, csmErr.NewInvalidParameterValueException("InstanceId and Region are required")
	}

	// gateway monitoring information can only be collected under the same vpc
	cpromAgent, getErr := service.MonitorModel.GetCPromAgentByVpcID(ctx, cpromInstance.InstanceId, cpromInstance.Region, vpcID)
	if getErr != nil {
		return nil, getErr
	}

	jobID, createErr := service.MonitorModel.CreateGatewayScrapeJob(ctx, csmInstanceID, cpromInstance.Region,
		cpromInstance.InstanceId, cpromAgent.AgentID)
	if createErr != nil {
		return nil, createErr
	}
	// TODO 待明确网格指标后创建聚合任务

	// 构建返回结构，保存采集任务ID和agentID
	cpromGatewayInfo := &meta.CPromGatewayInfo{
		ID:          cpromInstance.InstanceId,
		Region:      cpromInstance.Region,
		ScrapeJobID: jobID,
		AgentID:     cpromAgent.AgentID,
	}
	return cpromGatewayInfo, nil
}

// PrometheusRemoteConfig prometheus远程存储配置
type prometheusRemoteConfig struct {
	RemoteReadUrl string `json:"remoteReadUrl"`
	BearerToken   string `json:"bearerToken"`
	InstanceId    string `json:"instanceId"`
}

// QueryPrometheusMetrics prometheus指标查询接口
func (service *Service) QueryPrometheusMetrics(ctx csmContext.CsmContext, instanceId, metricType, query, step, start, end string) (interface{}, error) {
	ctx.CsmLogger().Infof("开始查询prometheus指标, instanceId=%s, query=%s, step=%s, start=%s, end=%s",
		instanceId, query, step, start, end)

	var remoteConfig *prometheusRemoteConfig
	if metricType == "basic" {
		remoteConfig = &prometheusRemoteConfig{
			RemoteReadUrl: constants.GetPrometheusRemoteReadUrl(),
			BearerToken:   constants.GetPrometheusBearerToken(),
			InstanceId:    constants.GetPrometheusInstanceId(),
		}
	} else {
		// 1. 从新数据库中查询AI网关实例的信息
		gatewayInfo, err := service.AIGatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
		if err != nil {
			return nil, csmErr.NewServiceException("failed to get AI gateway info", err)
		}
		if gatewayInfo == nil || *gatewayInfo == nil {
			return nil, csmErr.NewInvalidParameterValueException("AI gateway instance not found")
		}

		// 提取网关信息
		gateway := **gatewayInfo
		region := gateway.Region
		userNamespace := gateway.Namespace
		hostedClusterId := gateway.HostedClusterID
		hostedClusterName := gateway.HostedClusterName

		ctx.CsmLogger().Infof("获取到AI网关实例信息: region=%s, userNamespace=%s, hostedClusterId=%s, hostedClusterName=%s",
			region, userNamespace, hostedClusterId, hostedClusterName)

		// 2. 验证托管集群信息
		if len(hostedClusterId) == 0 {
			return nil, csmErr.NewInvalidParameterValueException("no hosting cluster found for this AI gateway instance")
		}

		// 3. 获取prometheus远程存储配置
		remoteConfig, err = service.getPrometheusRemoteConfig(ctx, region, userNamespace, hostedClusterId)
		if err != nil {
			ctx.CsmLogger().Errorf("获取远程存储配置失败: %v", err)
			return nil, err
		}
	}

	ctx.CsmLogger().Infof("获取到远程存储配置: remoteReadUrl=%s, instanceId=%s",
		remoteConfig.RemoteReadUrl, remoteConfig.InstanceId)

	// 4. 调用远程存储的query_range接口
	result, err := service.callPrometheusRemoteRead(ctx, remoteConfig, query, step, start, end)
	if err != nil {
		ctx.CsmLogger().Errorf("调用远程存储接口失败: %v", err)
		return nil, err
	}

	ctx.CsmLogger().Infof("成功获取prometheus指标数据")
	return result, nil
}

// getPrometheusConfigMap 获取ConfigMap配置
func (service *Service) getPrometheusConfigMap(ctx csmContext.CsmContext, region, hostedClusterId string) (*v1.ConfigMap, error) {
	// 创建k8s客户端
	hostingClient, err := service.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return nil, csmErr.NewServiceException("failed to create cluster client", err)
	}

	// 查询monitoring命名空间下的vmagent-cluster-config configmap
	configMap, err := hostingClient.Kube().CoreV1().ConfigMaps("monitoring").Get(
		context.TODO(),
		"vmagent-cluster-config",
		metav1.GetOptions{},
	)
	if err != nil {
		ctx.CsmLogger().Errorf("获取configmap失败: %v", err)
		return nil, csmErr.NewServiceException("failed to get vmagent-cluster-config", err)
	}

	return configMap, nil
}

// parsePrometheusConfig 解析Prometheus配置
func (service *Service) parsePrometheusConfig(configMap *v1.ConfigMap) ([]interface{}, error) {
	// 解析prometheus.yaml配置
	prometheusYaml, exists := configMap.Data["prometheus.yaml"]
	if !exists {
		return nil, csmErr.NewServiceException("prometheus.yaml not found in configmap", nil)
	}

	// 解析yaml配置获取remote_write信息
	var prometheusConfig map[string]interface{}
	err := yaml.Unmarshal([]byte(prometheusYaml), &prometheusConfig)
	if err != nil {
		return nil, csmErr.NewServiceException("failed to parse prometheus config", err)
	}

	// 提取remote_write配置
	remoteWrites, exists := prometheusConfig["remote_write"]
	if !exists {
		return nil, csmErr.NewServiceException("remote_write config not found", nil)
	}

	remoteWriteList, ok := remoteWrites.([]interface{})
	if !ok || len(remoteWriteList) == 0 {
		return nil, csmErr.NewServiceException("invalid remote_write config", nil)
	}

	return remoteWriteList, nil
}

// checkUserNamespaceMatch 检查relabel config是否匹配user namespace
func (service *Service) checkUserNamespaceMatch(relabelConfig map[string]interface{}, userNamespace string) bool {
	sourceLabels, exists := relabelConfig["source_labels"]
	if !exists {
		return false
	}

	sourceLabelsList, ok := sourceLabels.([]interface{})
	if !ok || len(sourceLabelsList) == 0 {
		return false
	}

	// 检查是否包含user_namespace
	hasUserNamespace := false
	for _, label := range sourceLabelsList {
		if labelStr, ok := label.(string); ok && labelStr == "user_namespace" {
			hasUserNamespace = true
			break
		}
	}

	if !hasUserNamespace {
		return false
	}

	// 检查regex是否匹配
	regex, exists := relabelConfig["regex"]
	if !exists {
		return false
	}

	regexStr, ok := regex.(string)
	if !ok {
		return false
	}

	// 去除regex前后的空格
	regexStr = strings.TrimSpace(regexStr)

	// 检查userNamespace是否匹配regex
	matched, err := regexp.MatchString(regexStr, userNamespace)
	if err != nil {
		return false
	}

	return matched
}

// findMatchingRemoteWrite 查找匹配的remote_write配置
func (service *Service) findMatchingRemoteWrite(
	ctx csmContext.CsmContext, remoteWriteList []interface{}, userNamespace string) (map[string]interface{}, error) {
	for _, rw := range remoteWriteList {
		remoteWrite, ok := rw.(map[string]interface{})
		if !ok {
			continue
		}

		// 检查write_relabel_configs
		writeRelabelConfigs, exists := remoteWrite["write_relabel_configs"]
		if !exists {
			continue
		}

		relabelConfigList, ok := writeRelabelConfigs.([]interface{})
		if !ok {
			continue
		}

		// 查找匹配的relabel config
		for _, rc := range relabelConfigList {
			relabelConfig, ok := rc.(map[string]interface{})
			if !ok {
				continue
			}

			if service.checkUserNamespaceMatch(relabelConfig, userNamespace) {
				ctx.CsmLogger().Infof("找到匹配的remote_write配置, namespace=%q", userNamespace)
				return remoteWrite, nil
			}
		}
	}

	return nil, csmErr.NewServiceException(fmt.Sprintf("no matching remote_write config found for namespace %s", userNamespace), nil)
}

// extractRemoteWriteConfig 从remote_write配置中提取所需信息
func (service *Service) extractRemoteWriteConfig(targetRemoteWrite map[string]interface{}) (*prometheusRemoteConfig, error) {
	// 提取url
	writeUrl, exists := targetRemoteWrite["url"]
	if !exists {
		return nil, csmErr.NewServiceException("remote_write url not found", nil)
	}

	writeUrlStr, ok := writeUrl.(string)
	if !ok {
		return nil, csmErr.NewServiceException("invalid remote_write url", nil)
	}

	// 将write URL转换为read URL
	remoteReadUrl := strings.Replace(writeUrlStr, "/insert/prometheus/api/v1/write", "/select", 1)

	// 提取bearer_token
	bearerToken, exists := targetRemoteWrite["bearer_token"]
	if !exists {
		return nil, csmErr.NewServiceException("bearer_token not found", nil)
	}

	bearerTokenStr, ok := bearerToken.(string)
	if !ok {
		return nil, csmErr.NewServiceException("invalid bearer_token", nil)
	}

	// 提取headers中的InstanceId
	headers, exists := targetRemoteWrite["headers"]
	if !exists {
		return nil, csmErr.NewServiceException("headers not found", nil)
	}

	headersMap, ok := headers.(map[string]interface{})
	if !ok {
		return nil, csmErr.NewServiceException("invalid headers", nil)
	}

	instanceIdValue, exists := headersMap["InstanceId"]
	if !exists {
		return nil, csmErr.NewServiceException("InstanceId not found in headers", nil)
	}

	instanceIdStr, ok := instanceIdValue.(string)
	if !ok {
		return nil, csmErr.NewServiceException("invalid InstanceId", nil)
	}

	return &prometheusRemoteConfig{
		RemoteReadUrl: remoteReadUrl,
		BearerToken:   bearerTokenStr,
		InstanceId:    instanceIdStr,
	}, nil
}

func (service *Service) getPrometheusRemoteConfig(ctx csmContext.CsmContext, region, userNamespace, hostedClusterId string) (*prometheusRemoteConfig, error) {
	ctx.CsmLogger().Infof("开始获取prometheus远程存储配置, region=%s, userNamespace=%s, hostedClusterId=%s",
		region, userNamespace, hostedClusterId)

	// 获取ConfigMap
	configMap, err := service.getPrometheusConfigMap(ctx, region, hostedClusterId)
	if err != nil {
		return nil, err
	}

	ctx.CsmLogger().Infof("成功获取configmap: %s", configMap.Name)

	// 解析配置
	remoteWriteList, err := service.parsePrometheusConfig(configMap)
	if err != nil {
		return nil, err
	}

	// 查找匹配的配置
	targetRemoteWrite, err := service.findMatchingRemoteWrite(ctx, remoteWriteList, userNamespace)
	if err != nil {
		return nil, err
	}

	// 提取配置信息
	config, err := service.extractRemoteWriteConfig(targetRemoteWrite)
	if err != nil {
		return nil, err
	}

	ctx.CsmLogger().Infof("解析得到配置: remoteReadUrl=%s, instanceId=%s",
		config.RemoteReadUrl, config.InstanceId)

	return config, nil
}

// callPrometheusRemoteRead 调用prometheus远程存储的query_range接口
func (service *Service) callPrometheusRemoteRead(ctx csmContext.CsmContext,
	config *prometheusRemoteConfig, query, step, start, end string) (interface{}, error) {

	ctx.CsmLogger().Infof("开始调用prometheus远程存储接口")

	// 构建请求URL
	requestUrl := fmt.Sprintf("%s/prometheus/api/v1/query_range", config.RemoteReadUrl)

	// 构建请求体（application/x-www-form-urlencoded格式）
	requestBody := fmt.Sprintf("query=%s&step=%s&start=%s&end=%s",
		query, step, start, end)

	ctx.CsmLogger().Infof("请求URL: %s", requestUrl)
	ctx.CsmLogger().Infof("请求体: %s", requestBody)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", requestUrl, strings.NewReader(requestBody))
	if err != nil {
		return nil, csmErr.NewServiceException("failed to create request", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", config.BearerToken))
	req.Header.Set("InstanceId", config.InstanceId)

	ctx.CsmLogger().Infof("设置请求头: Content-Type=application/x-www-form-urlencoded, Authorization=Bearer %s..., InstanceId=%s",
		config.BearerToken[:20], config.InstanceId)

	// 发送HTTP请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		ctx.CsmLogger().Errorf("HTTP请求失败: %v", err)
		return nil, csmErr.NewServiceException("failed to call remote prometheus", err)
	}
	defer resp.Body.Close()

	ctx.CsmLogger().Infof("HTTP响应状态码: %d", resp.StatusCode)

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		ctx.CsmLogger().Errorf("读取响应体失败: %v", err)
		return nil, csmErr.NewServiceException("failed to read response", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		ctx.CsmLogger().Errorf("远程接口返回错误状态码: %d, 响应体: %s", resp.StatusCode, string(body))
		return nil, csmErr.NewServiceException(fmt.Sprintf("remote API returned status %d", resp.StatusCode), nil)
	}

	// 解析JSON响应
	var result interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		ctx.CsmLogger().Errorf("解析JSON响应失败: %v", err)
		return nil, csmErr.NewServiceException("failed to parse response JSON", err)
	}

	ctx.CsmLogger().Infof("成功调用prometheus远程存储接口")

	return result, nil
}
