package registercenter

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"github.com/baidubce/bce-sdk-go/services/esg"
	"github.com/golang/protobuf/proto"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	"github.com/spf13/viper"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/msesdk"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc"

	v1 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/crd/apis/registercenter/v1"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/register"
	registercentersdk2 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/registercentersdk"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type Service struct {
	opt                   *Option
	cceService            cce.ClientInterface
	registerInstanceModel register.ServiceInterface
	VpcService            vpc.ServiceInterface
	instanceIdToAccountId sync.Map
	mseClients            sync.Map
	activateAccountId     sync.Map
}

func NewRegisterCenterService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:                   option,
		cceService:            cce.NewClientService(),
		registerInstanceModel: register.NewRegisterInstancesService(register.NewOption(gormDB)),
		VpcService:            vpc.NewVPCService(),
		mseClients:            sync.Map{},
		instanceIdToAccountId: sync.Map{},
		activateAccountId:     sync.Map{},
	}
}

func (s *Service) GetServiceList(ctx csmContext.CsmContext, req *registercentersdk2.RegisterCenterServiceListRequest) (
	*registercentersdk2.RegisterCenterServiceListResponse, error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return nil, csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	if registerInstance == nil {
		return nil, csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	return s.opt.Client.GetRegisterCenterServiceList(ctx, req, option)
}

func (s *Service) CreateServiceInstance(ctx csmContext.CsmContext, req *registercentersdk2.CreateServiceInstanceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.CreateRegisterServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) UpdateServiceInstance(ctx csmContext.CsmContext, req *registercentersdk2.CreateServiceInstanceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.UpdateRegisterServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) DeleteServiceInstance(ctx csmContext.CsmContext, req *registercentersdk2.DeleteServiceInstanceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.DeleteRegisterServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) GetServiceInstanceList(ctx csmContext.CsmContext, req *registercentersdk2.ServiceInstanceListRequest) (
	*registercentersdk2.ServiceInstanceListResponse, error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return nil, csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	if registerInstance == nil {
		return nil, csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.GetServiceInstanceList(ctx, req, option)
	if err != nil {
		return nil, err
	}
	return response, registercentersdk2.NewPolarisError(response)
}

func intToBool(i int) bool {
	if i == 0 {
		return false
	}
	return true
}

func (s *Service) NewRegisterInstance(ctx csmContext.CsmContext, instance *meta.RegisterInstance, monitorToken, esgId string) error {
	region := viper.GetString(Region)
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return csmErr.NewUnauthorizedException("user is nil", err)
	}

	// 获取esgName
	esgName := ""
	if esgId != "" {
		esgName, err = s.getEsgNameById(ctx, esgId)
		if err != nil {
			return err
		}
	}

	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return err
	}

	// 3. create register instance CR
	cr := &v1.RegisterInstance{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Registry",
			APIVersion: "cse.baidubce.com/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: instance.InstanceId,
			Labels: map[string]string{
				"bce-account-id": accountId,
			},
		},
		Spec: v1.RegisterInstanceSpec{
			AccountID: accountId,
			VpcID:     instance.VpcId,
			SubnetID:  instance.SubnetId,
			ServerSpec: v1.ServerSpec{
				CPU:      "100m",
				Memory:   "500Mi",
				Replicas: 1,
			},
			EsgID:        esgId,
			EsgName:      esgName,
			Release:      viper.GetString(Release),
			PublicAccess: s.buildPublicAccessConfig(instance),
		},
	}

	enable := intToBool(instance.MonitorEnabled)
	cr.Spec.Args = &v1.Args{
		MonitorEnable: &enable,
	}

	if enable {
		cr.Spec.Args.MonitorCpromID = instance.MonitorInstanceId
		cr.Spec.Args.MonitorCpromToken = monitorToken
	}

	// 4. create register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}

	ins, _ := json.Marshal(cr)

	ctx.CsmLogger().Infof("create register instance CR: %s", string(ins))

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(cr)
	if err != nil {
		// handle error
		return err
	}
	unstructuredCr := &unstructured.Unstructured{Object: unstructuredObj}
	_, err = client.Dynamic().Resource(gvr).Create(context.TODO(), unstructuredCr, metav1.CreateOptions{})
	if err != nil {
		// handle error
		return err
	}

	// todo install vmagent

	// insert db
	err = s.registerInstanceModel.NewRegisterInstance(ctx, instance)
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) GetRegisterKubeClient(ctx csmContext.CsmContext, region string) (kube.Client, error) {
	// 1. get clusterId from config yaml
	clusterId := viper.GetString(RegisterCluster)

	ctx.CsmLogger().Infof("get clusterId: %s from config", clusterId)

	// 2. get admin kube config & create k8s client
	client, err := s.cceService.NewClient(ctx, region, clusterId, meta.HostingMeshType)
	if err != nil {
		return nil, err
	}
	return client, nil
}

func (s *Service) UpdateRegisterInstance(ctx csmContext.CsmContext, instanceId, endpointId, esgId string) error {
	// 0. 参数校验
	esgName, err := s.getEsgNameById(ctx, esgId)
	if err != nil {
		return err
	}

	// 1. 获取cr
	cr, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	// 2. 写安全组id到cr
	cr.Spec.EsgID = esgId
	cr.Spec.EsgName = esgName

	// 4. 提交cr
	if err = s.PutRegisterCenterCR(ctx, cr); err != nil {
		return err
	}

	return nil
}

func (s *Service) DeleteRegisterInstanceByInstanceId(ctx csmContext.CsmContext, id string) error {
	err := s.registerInstanceModel.DeleteRegisterInstanceByInstanceId(ctx, id)
	if err != nil {
		return err
	}

	err = s.DeleteRegisterCenterCR(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) GetRegisterInstances(ctx csmContext.CsmContext, query *meta.QueryRegisterInstance) (*meta.PageResponse, error) {
	pageRes, insList, err := s.registerInstanceModel.GetRegisterInstances(ctx, query)
	if err != nil {
		return nil, err
	}

	// get cr by label bce-account-id
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}

	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	// get register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}
	labelSelector := "bce-account-id=" + accountId
	listOptions := metav1.ListOptions{
		LabelSelector: labelSelector,
	}
	unstructuredList, err := client.Dynamic().Resource(gvr).List(context.TODO(), listOptions)
	ctx.CsmLogger().Infof("get register instance CR list by selector: %s, list size: %s", labelSelector, len(unstructuredList.Items))
	if err != nil {
		ctx.CsmLogger().Errorf("get register instance CR list err: %s", err.Error())
	}

	registerInstances := make([]v1.RegisterInstance, 0)
	for _, u := range unstructuredList.Items {
		var registerInstance v1.RegisterInstance
		err := runtime.DefaultUnstructuredConverter.FromUnstructured(u.Object, &registerInstance)
		if err != nil {
			// handle error
			return nil, err
		}
		registerInstances = append(registerInstances, registerInstance)
	}

	instanceStatusMap := make(map[string]v1.RegisterInstance)
	for _, registerInstance := range registerInstances {
		instanceStatusMap[registerInstance.Name] = registerInstance
	}

	latestRelease := viper.GetString(Release)
	registerInsRepsList := make([]meta.RegisterInsListRep, 0)
	for _, ins := range *insList {
		registryStatus := instanceStatusMap[ins.InstanceId]
		serverPort, serverProtocol := registryStatus.GetServerPortProtocol()
		registerInsRepsList = append(registerInsRepsList,
			meta.RegisterInsListRep{
				Id:             ins.InstanceId,
				Name:           ins.InstanceName,
				Region:         ins.Region,
				Status:         int(convertStatus(registryStatus.Status.Phase)),
				ServerProtocol: serverProtocol,
				ServerPort:     serverPort,
				CreateTime:     *ins.CreateTime,
				UpdateTime:     *ins.UpdateTime,
				Release:        registryStatus.Status.Release,
				Latest:         registryStatus.Status.Release == latestRelease,
			})
	}

	return &meta.PageResponse{
		PageNo:     pageRes.PageNo,
		PageSize:   pageRes.PageSize,
		TotalCount: pageRes.TotalCount,
		Result:     registerInsRepsList,
	}, nil
}

func (s *Service) GetRegisterInstancesByInstanceId(ctx csmContext.CsmContext, instanceId string) (*meta.RegisterInsDetailRep, error) {

	registerInstanceModel, err := s.registerInstanceModel.GetRegisterInstancesByInstanceId(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	registerInsDetail := &meta.RegisterInsDetailRep{
		Id:                registerInstanceModel.InstanceId,
		Name:              registerInstanceModel.InstanceName,
		Region:            registerInstanceModel.Region,
		Status:            registerInstanceModel.Status,
		CreateTime:        *registerInstanceModel.CreateTime,
		UpdateTime:        *registerInstanceModel.UpdateTime,
		MonitorInstanceId: registerInstanceModel.MonitorInstanceId,
		ServerPort:        registerInstanceModel.ServerPort,
		ServerProtocol:    registerInstanceModel.ServerProtocol,
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	registerInsDetail.Release = registerInstance.Status.Release
	registerInsDetail.Latest = registerInstance.Status.Release == viper.GetString(Release)
	registerInsDetail.ServerPort, registerInsDetail.ServerProtocol = registerInstance.GetServerPortProtocol()

	// if monitor enable, set monitor instance id
	if registerInstance.Spec.Args.MonitorEnable != nil &&
		*registerInstance.Spec.Args.MonitorEnable &&
		len(registerInstance.Spec.Args.MonitorCpromID) > 0 {
		registerInsDetail.MonitorInstanceId = registerInstance.Spec.Args.MonitorCpromID
	}

	// status
	status := convertStatus(registerInstance.Status.Phase)
	registerInsDetail.Status = int(status)

	// loadBalancer
	loadBalances := make([]meta.RegisterLoadBalancer, 0)

	// get load balancer from register instance CR
	vpcDetail, err := s.VpcService.GetVPCAndSubnetDetail(ctx, registerInstanceModel.VpcId,
		registerInstanceModel.SubnetId, viper.GetString(Region))
	if err != nil {
		ctx.CsmLogger().Errorf("get vpc detail err: %s", err.Error())
	}
	for _, endpoint := range registerInstance.Status.EndpointList {
		if endpoint.Type == v1.EndpointTypePublic {
			continue
		}
		var vpcName, subnetName string
		if vpcDetail != nil {
			vpcName = vpcDetail.VpcNetworkName
			subnetName = vpcDetail.SubnetName
		}

		loadBalances = append(loadBalances, meta.RegisterLoadBalancer{
			Type:       string(endpoint.Type),
			Ip:         endpoint.IP,
			VpcId:      registerInstanceModel.VpcId,
			VpcName:    vpcName,
			SubnetId:   registerInstanceModel.SubnetId,
			SubnetName: subnetName,
			Id:         endpoint.ID,
			EsgId:      registerInstance.Spec.EsgID,
			EsgName:    registerInstance.Spec.EsgName,
		})
	}
	registerInsDetail.LoadBalanceList = loadBalances
	registerInsDetail.AccountId = registerInstance.Spec.AccountID

	// 处理公网访问信息（支持配置保留和回显）
	registerInsDetail.PublicAccess = s.buildPublicAccessInfo(registerInstance)

	return registerInsDetail, nil

}

func (s *Service) PutRegisterCenterCR(ctx csmContext.CsmContext, cr *v1.RegisterInstance) error {
	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return err
	}
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}

	unstructuredObj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(cr)
	if err != nil {
		return err
	}
	unstructuredCr := &unstructured.Unstructured{Object: unstructuredObj}

	_, err = client.Dynamic().Resource(gvr).Update(context.TODO(), unstructuredCr, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) GetRegisterCenterCR(ctx csmContext.CsmContext, instanceId string) (*v1.RegisterInstance, error) {

	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return nil, err
	}

	// get register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}
	unstructuredObj, err := client.Dynamic().Resource(gvr).Get(context.TODO(), instanceId, metav1.GetOptions{})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			// handle not found error
			ctx.CsmLogger().Errorf("register instance: %s not found", instanceId)
			return nil, nil
		} else {
			// handle other errors
			return nil, err
		}
	}
	var registerInstance v1.RegisterInstance
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(unstructuredObj.Object, &registerInstance)
	if err != nil {
		// handle error
		return nil, err
	}
	return &registerInstance, nil
}

func convertStatus(crStatus v1.CCRPhase) meta.RegisterInstanceStatus {
	switch crStatus {
	case v1.CCRPending, v1.CCRCreating:
		return meta.CreatingStatus
	case v1.CCRRunning, v1.CCRCreated, v1.CCRUpgraded:
		return meta.RunningStatus
	case v1.CCRUpgrading:
		return meta.AdjustingStatus
	case v1.CCRStopping, v1.CCRTerminating:
		return meta.ReleasingStatus
	case v1.CCRStartingFailed:
		return meta.CreateFailedStatus
	case v1.CCRFailed, v1.CCRUpgradeFailed, v1.CCRStoppingFailed:
		return meta.ErrorStatus
	default:
		return meta.InitStatus
	}
}

func (s *Service) UpdateRegisterInstanceArgs(ctx csmContext.CsmContext, instanceId string, args map[string]interface{}) error {
	// 1. 获取cr
	cr, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	// 2. 写args到cr
	updated, err := cr.WriteArgs(args)
	if err != nil {
		return err
	}

	// 3. 不需要更新直接退出
	if !updated {
		return nil
	}

	// 4. 提交cr
	if err = s.PutRegisterCenterCR(ctx, cr); err != nil {
		return err
	}

	return nil
}

func (s *Service) DeleteRegisterCenterCR(ctx csmContext.CsmContext, instanceId string) error {

	region := viper.GetString(Region)
	client, err := s.GetRegisterKubeClient(ctx, region)
	if err != nil {
		return err
	}

	// get register instance CR
	gvr := schema.GroupVersionResource{
		Group:    "cse.baidubce.com",
		Version:  "v1",
		Resource: "registries",
	}
	unstructuredObj, err := client.Dynamic().Resource(gvr).Get(context.TODO(), instanceId, metav1.GetOptions{})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			// handle not found error
			ctx.CsmLogger().Errorf("register instance CR: %s not found", instanceId)
			return nil
		} else {
			// handle other errors
			return err
		}
	}
	var registerInstance v1.RegisterInstance
	err = runtime.DefaultUnstructuredConverter.FromUnstructured(unstructuredObj.Object, &registerInstance)
	if err != nil {
		// handle error
		return err
	}

	if registerInstance.Status.Phase == v1.CCRTerminating {
		ctx.CsmLogger().Infof("register instance CR: %s is terminating", instanceId)
		return nil
	}

	// delete register instance CR
	err = client.Dynamic().Resource(gvr).Delete(context.TODO(), instanceId, metav1.DeleteOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) GetRegisterInstanceArgs(ctx csmContext.CsmContext, instanceId string) (map[string]interface{}, error) {
	cr, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}
	return cr.ReadArgs(), nil
}

func (s *Service) BatchDeleteServiceInstance(ctx csmContext.CsmContext, req []*registercentersdk2.BatchRequest) error {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.BatchDeleteServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) BatchIsolateServiceInstance(ctx csmContext.CsmContext, req []*registercentersdk2.BatchRequest) error {
	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.BatchIsolateServiceInstance(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

// getEsgNameById 通过安全组id获取安全组name
func (s *Service) getEsgNameById(ctx csmContext.CsmContext, esgId string) (string, error) {
	if esgId == "" {
		return "", fmt.Errorf("empty enterprise security group id")
	}

	esgName := ""
	marker := ""
	for {
		result, err := s.VpcService.ListEsg(ctx, &esg.ListEsgArgs{
			MaxKeys: 1000,
			Marker:  marker,
		}, viper.GetString(Region))
		if err != nil {
			return "", err
		}
		marker = result.NextMarker
		for _, esgInfo := range result.EnterpriseSecurityGroups {
			if esgInfo.Id == esgId {
				esgName = esgInfo.Name
				break
			}
		}
		if esgName != "" || !result.IsTruncated {
			break
		}
	}

	if esgName == "" {
		return "", fmt.Errorf("enterprise security group %s not found", esgId)
	}
	return esgName, nil
}

func (s *Service) GetNamespaceList(ctx csmContext.CsmContext,
	req *registercentersdk2.RegisterCenterNamespaceListRequest) (
	*apiservice.BatchQueryResponse, error) {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return nil, csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}

	if registerInstance == nil {
		return nil, csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}
	response, err := s.opt.Client.GetRegisterCenterNamespaceList(ctx, req, option)
	if err != nil {
		return nil, err
	}
	return response, registercentersdk2.NewPolarisError(response)
}

func (s *Service) CreateNamespaces(ctx csmContext.CsmContext, req []registercentersdk2.CreateNamespaceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.CreateRegisterNamespaces(ctx, req, option)
	if err != nil {
		return err
	}

	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) UpdateNamespaces(ctx csmContext.CsmContext, req []registercentersdk2.CreateNamespaceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.UpdateRegisterNamespaces(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) DeleteNamespaces(ctx csmContext.CsmContext, req []registercentersdk2.CreateNamespaceRequest) error {

	// 请求参数校验
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterValueException("register instanceId could not be nil")
	}

	registerInstance, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	if registerInstance == nil {
		return csmErr.NewUnknownError(fmt.Errorf("register instance %s not found", instanceId))
	}

	option := &registercentersdk2.AuthOption{
		Token:    registerInstance.Status.Token,
		Endpoint: fmt.Sprintf("%s:%d", registerInstance.Status.ServerBLB.BRegionIP, registerInstance.Status.ServerBLB.Port),
	}

	response, err := s.opt.Client.DeleteRegisterNamespaces(ctx, req, option)
	if err != nil {
		return err
	}
	return registercentersdk2.NewPolarisError(response)
}

func (s *Service) GetInstanceAccountId(ctx csmContext.CsmContext, instanceId string) (string, error) {
	if instanceId == "" {
		return "", fmt.Errorf("instanceId empty")
	}
	value, ok := s.instanceIdToAccountId.Load(instanceId)
	if ok {
		return value.(string), nil
	}

	registerInstanceModel, err := s.registerInstanceModel.GetRegisterInstancesByInstanceId(ctx, instanceId)
	if err != nil {
		if strings.Contains(err.Error(), "record not found") {
			return "", nil
		}
		return "", err
	}

	accountId := registerInstanceModel.AccountId
	s.instanceIdToAccountId.Store(instanceId, accountId)
	return accountId, nil
}

func (s *Service) PostActivate(ctx csmContext.CsmContext) error {
	return s.registerInstanceModel.PostActivate(ctx)
}

func (s *Service) GetActivate(ctx csmContext.CsmContext) error {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return csmErr.NewUnauthorizedException("user is nil", err)
	}
	_, ok := s.activateAccountId.Load(accountId)
	if ok {
		return nil
	}
	if err = s.registerInstanceModel.GetActivate(ctx); err != nil {
		return err
	}
	s.activateAccountId.Store(accountId, 1)
	return nil
}

func (s *Service) GetMseClient(ctx csmContext.CsmContext, instanceId string) (*msesdk.Client, error) {
	if instanceId == "" {
		return nil, fmt.Errorf("instanceId empty")
	}
	value, ok := s.mseClients.Load(instanceId)
	if ok {
		return value.(*msesdk.Client), nil
	}

	registry, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return nil, err
	}
	if registry == nil {
		return nil, err
	}
	status := registry.Status
	if status.ServerBLB == nil || status.ServerBLB.BRegionIP == "" || status.ServerBLB.Port == 0 || status.Token == "" {
		return nil, csmErr.NewServiceUnavailableException("registry not ready")
	}
	endpoint := fmt.Sprintf("%s:%d", status.ServerBLB.BRegionIP, status.ServerBLB.Port)
	client := msesdk.NewClient(endpoint, status.Token, status.Release)
	s.mseClients.Store(instanceId, client)
	return client, nil
}

func (s *Service) MseCommonRequest(ctx csmContext.CsmContext, req msesdk.Request, resp proto.Message) error {
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewInvalidParameterInputValueException("instanceId is required")
	}
	client, err := s.GetMseClient(ctx, instanceId)
	if err != nil {
		return err
	}
	decorator := msesdk.NewClientDecorator(client, req)

	if _, err = decorator.DoRequest(ctx, resp); err != nil {
		return err
	}
	return nil
}

// buildPublicAccessConfig 构建公网访问配置
func (s *Service) buildPublicAccessConfig(instance *meta.RegisterInstance) *v1.PublicAccessConfig {
	if instance.PublicAccessEnabled == nil || *instance.PublicAccessEnabled == 0 {
		return nil
	}

	// 解析白名单
	var whiteList []string
	if instance.PublicWhiteList != "" {
		// 白名单已经在API层转换为CIDR格式的逗号分隔字符串
		whiteList = strings.Split(instance.PublicWhiteList, ",")
		for i, cidr := range whiteList {
			whiteList[i] = strings.TrimSpace(cidr)
		}
	}

	// 构建默认域名
	publicDomain := s.buildDefaultDomain(instance.InstanceId, instance.Region)
	if instance.PublicCustomDomain != "" {
		publicDomain = instance.PublicCustomDomain
	}

	return &v1.PublicAccessConfig{
		Enabled:       true,
		BillingMethod: instance.PublicBillingMethod,
		BandwidthMbps: instance.PublicBandwidthMbps,
		WhiteList:     whiteList,
		CustomDomain:  publicDomain,
	}
}

// buildDefaultDomain 构建默认域名
func (s *Service) buildDefaultDomain(instanceId, region string) string {
	return fmt.Sprintf("%s.%s.baidubce.com", instanceId, region)
}

// buildPublicAccessInfo 构建公网访问信息（支持配置保留和回显）
func (s *Service) buildPublicAccessInfo(registerInstance *v1.RegisterInstance) *meta.PublicAccessInfo {
	// 如果没有配置公网访问，返回nil
	if registerInstance.Spec.PublicAccess == nil {
		return nil
	}

	// 构建基础配置信息（无论是否启用都返回）
	publicAccessInfo := &meta.PublicAccessInfo{
		Enabled:       registerInstance.Spec.PublicAccess.Enabled,
		BillingMethod: registerInstance.Spec.PublicAccess.BillingMethod,
		BandwidthMbps: registerInstance.Spec.PublicAccess.BandwidthMbps,
		WhiteList:     registerInstance.Spec.PublicAccess.WhiteList,
		CustomDomain:  registerInstance.Spec.PublicAccess.CustomDomain,
	}

	// 运行时状态信息（公网IP、状态、域名）从Status获取
	if registerInstance.Status.PublicAccess != nil {
		publicAccessInfo.Status = registerInstance.Status.PublicAccess.Status
		publicAccessInfo.PublicIP = registerInstance.Status.PublicAccess.PublicIP           // 必须从Status获取
		publicAccessInfo.PublicDomain = registerInstance.Status.PublicAccess.PublicDomain  // 运行时域名从Status获取

		// 修复：不再从Status覆盖配置信息（BillingMethod、BandwidthMbps、WhiteList、CustomDomain）
		// 这些配置信息始终从Spec读取，确保用户修改后立即可见，解决白名单更新响应时间不一致问题
	} else {
		// 没有状态信息时，设置默认状态
		if publicAccessInfo.Enabled {
			publicAccessInfo.Status = "opening" // 可能正在创建中
		} else {
			publicAccessInfo.Status = "closed"
		}
	}

	return publicAccessInfo
}

// UpdatePublicAccessConfig 更新公网访问配置（支持配置保留和智能合并）
func (s *Service) UpdatePublicAccessConfig(ctx csmContext.CsmContext, instanceId string, req *UpdatePublicAccessRequest) error {
	// 1. 获取当前实例的CR
	cr, err := s.GetRegisterCenterCR(ctx, instanceId)
	if err != nil {
		return err
	}

	// 2. 使用配置管理器进行智能配置合并
	configManager := NewPublicAccessConfigManager(cr)

	// 构建新的配置请求
	newConfig := &v1.PublicAccessConfig{}
	if req.EnablePublicAccess != nil {
		newConfig.Enabled = *req.EnablePublicAccess
	} else if cr.Spec.PublicAccess != nil {
		// 保持当前启用状态
		newConfig.Enabled = cr.Spec.PublicAccess.Enabled
	}

	// 设置其他配置字段
	if req.BillingMethod != "" {
		newConfig.BillingMethod = req.BillingMethod
	}
	if req.BandwidthMbps > 0 {
		newConfig.BandwidthMbps = req.BandwidthMbps
	}
	if len(req.WhiteList) > 0 {
		newConfig.WhiteList = req.WhiteList
	}
	if req.CustomDomain != "" {
		newConfig.CustomDomain = req.CustomDomain
	}

	// 3. 智能合并配置
	mergedConfig := configManager.MergeConfig(newConfig)

	// 4. 验证配置
	if err := configManager.ValidateConfig(mergedConfig); err != nil {
		return err
	}

	// 5. 更新CR的Spec
	cr.Spec.PublicAccess = mergedConfig

	// 6. 提交更新
	if err := s.PutRegisterCenterCR(ctx, cr); err != nil {
		return err
	}

	return nil
}

// UpdatePublicAccessRequest 更新公网访问配置的请求结构
type UpdatePublicAccessRequest struct {
	EnablePublicAccess *bool    `json:"enablePublicAccess,omitempty"`
	BillingMethod      string   `json:"billingMethod,omitempty"`
	BandwidthMbps      int      `json:"bandwidthMbps,omitempty"`
	WhiteList          []string `json:"whiteList,omitempty"`
	CustomDomain       string   `json:"customDomain,omitempty"`
}

// NewPublicAccessConfigManager 创建公网访问配置管理器
func NewPublicAccessConfigManager(registry *v1.RegisterInstance) *PublicAccessConfigManager {
	return &PublicAccessConfigManager{
		registry: registry,
	}
}

// PublicAccessConfigManager 公网访问配置管理器
type PublicAccessConfigManager struct {
	registry *v1.RegisterInstance
}

// MergeConfig 智能合并配置（支持配置保留和恢复）
func (m *PublicAccessConfigManager) MergeConfig(newConfig *v1.PublicAccessConfig) *v1.PublicAccessConfig {
	if newConfig == nil {
		return nil
	}

	// 获取当前保存的配置
	currentConfig := m.registry.Spec.PublicAccess

	// 创建合并后的配置
	mergedConfig := &v1.PublicAccessConfig{
		Enabled: newConfig.Enabled,
	}

	// 智能合并其他配置字段
	if newConfig.BillingMethod != "" {
		mergedConfig.BillingMethod = newConfig.BillingMethod
	} else if currentConfig != nil && currentConfig.BillingMethod != "" {
		// 保留原有配置
		mergedConfig.BillingMethod = currentConfig.BillingMethod
	} else {
		// 使用默认值
		mergedConfig.BillingMethod = "ByTraffic"
	}

	if newConfig.BandwidthMbps > 0 {
		mergedConfig.BandwidthMbps = newConfig.BandwidthMbps
	} else if currentConfig != nil && currentConfig.BandwidthMbps > 0 {
		// 保留原有配置
		mergedConfig.BandwidthMbps = currentConfig.BandwidthMbps
	} else if mergedConfig.BillingMethod == "ByBandwidth" {
		// 按带宽计费时的默认值
		mergedConfig.BandwidthMbps = 100
	}

	if len(newConfig.WhiteList) > 0 {
		mergedConfig.WhiteList = newConfig.WhiteList
	} else if currentConfig != nil && len(currentConfig.WhiteList) > 0 {
		// 保留原有白名单配置
		mergedConfig.WhiteList = currentConfig.WhiteList
	}

	if newConfig.CustomDomain != "" {
		mergedConfig.CustomDomain = newConfig.CustomDomain
	} else if currentConfig != nil && currentConfig.CustomDomain != "" {
		// 保留原有域名配置
		mergedConfig.CustomDomain = currentConfig.CustomDomain
	}

	return mergedConfig
}

// ValidateConfig 验证配置的完整性和合法性
func (m *PublicAccessConfigManager) ValidateConfig(config *v1.PublicAccessConfig) error {
	if config == nil {
		return nil
	}

	if config.Enabled {
		// 开启公网访问时的必要验证
		if config.BillingMethod == "" {
			return fmt.Errorf("billingMethod is required when enablePublicAccess is true")
		}

		if config.BillingMethod == "ByBandwidth" && config.BandwidthMbps <= 0 {
			return fmt.Errorf("bandwidthMbps is required when billingMethod is ByBandwidth")
		}

		if config.BandwidthMbps > 500 {
			return fmt.Errorf("bandwidthMbps must be between 1 and 500")
		}

		// 验证自定义域名格式
		if config.CustomDomain != "" && !strings.HasSuffix(config.CustomDomain, ".baidubce.com") {
			return fmt.Errorf("customDomain must end with .baidubce.com")
		}
	}

	return nil
}
