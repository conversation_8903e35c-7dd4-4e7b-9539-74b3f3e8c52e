// nolint
package constants

import (
	"testing"

	"github.com/spf13/viper"
)

// TestGetAITokenLimitPluginName 是用于测试 GetAITokenLimitPluginName
// generated by Comate
func TestGetAITokenLimitPluginName(t *testing.T) {
	// 设置测试数据
	expectedPluginName := "testPlugin"
	viper.Set(aiTokenLimitPluginNameKey, expectedPluginName)

	// 调用被测方法
	actualPluginName := GetAITokenLimitPluginName()

	// 断言结果
	if actualPluginName != expectedPluginName {
		t.Errorf("Expected plugin name %s, but got %s", expectedPluginName, actualPluginName)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetAITokenLimitPluginPriority 是用于测试 GetAITokenLimitPluginPriority
// generated by Comate
func TestGetAITokenLimitPluginPriority(t *testing.T) {
	// 调用被测方法
	priority := GetAITokenLimitPluginPriority()

	// 验证返回值是否与预期的优先级一致
	expectedPriority := AITokenLimitPluginPriority
	if priority != expectedPriority {
		t.Errorf("Expected priority %d, but got %d", expectedPriority, priority)
	}
}

// TestGetAITokenLimitPluginURL 是用于测试 GetAITokenLimitPluginURL
// generated by Comate
func TestGetAITokenLimitPluginURL(t *testing.T) {
	// 设置测试环境
	viper.Set(aiTokenLimitPluginURLKey, "http://example.com/token-limit-plugin")

	// 调用被测方法
	url := GetAITokenLimitPluginURL()

	// 断言返回值
	expectedURL := "http://example.com/token-limit-plugin"
	if url != expectedURL {
		t.Errorf("Expected %s, but got %s", expectedURL, url)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetRedisServiceName 是用于测试 GetRedisServiceName
// generated by Comate
func TestGetRedisServiceName(t *testing.T) {
	// 设置默认环境
	viper.Set("aigw.redis.environment", "")
	viper.Set("aigw.redis.offline.service_name", "offline_service")
	viper.Set("aigw.redis.online.service_name", "online_service")

	// 测试默认环境
	env := GetRedisEnvironment()
	if env != DefaultRedisEnvironment {
		t.Errorf("Expected DefaultRedisEnvironment to be %s, but got %s", DefaultRedisEnvironment, env)
	}

	serviceName := GetRedisServiceName()
	if serviceName != "offline_service" {
		t.Errorf("Expected service_name to be %s, but got %s", "offline_service", serviceName)
	}

	// 设置在线环境
	viper.Set("aigw.redis.environment", "online")
	serviceName = GetRedisServiceName()
	if serviceName != "online_service" {
		t.Errorf("Expected service_name to be %s, but got %s", "online_service", serviceName)
	}

	// 清除环境设置
	viper.Set("aigw.redis.environment", "")
	viper.Set("aigw.redis.offline.service_name", "")
	viper.Set("aigw.redis.online.service_name", "")
}

// TestGetRedisPassword 是用于测试 GetRedisPassword
// generated by Comate
func TestGetRedisPassword(t *testing.T) {
	// 设置默认环境
	viper.Set("aigw.redis.environment", DefaultRedisEnvironment)
	viper.Set("aigw.redis.offline.password", "offlinePassword")

	// 测试默认环境下获取密码
	expectedPassword := "offlinePassword"
	actualPassword := GetRedisPassword()
	if actualPassword != expectedPassword {
		t.Errorf("Expected password %s, but got %s", expectedPassword, actualPassword)
	}

	// 设置不同的环境
	viper.Set("aigw.redis.environment", "production")
	viper.Set("aigw.redis.production.password", "productionPassword")

	// 测试不同环境下获取密码
	expectedPassword = "productionPassword"
	actualPassword = GetRedisPassword()
	if actualPassword != expectedPassword {
		t.Errorf("Expected password %s, but got %s", expectedPassword, actualPassword)
	}

	// 设置环境为空字符串
	viper.Set("aigw.redis.environment", "")
	expectedPassword = "offlinePassword"
	actualPassword = GetRedisPassword()
	if actualPassword != expectedPassword {
		t.Errorf("Expected password %s, but got %s", expectedPassword, actualPassword)
	}
}

// TestGetTokenRateLimitTemplatePath 是用于测试 GetTokenRateLimitTemplatePath
// generated by Comate
func TestGetTokenRateLimitTemplatePath(t *testing.T) {
	// 设置测试数据
	expectedPath := "/path/to/template"
	viper.Set(tokenRateLimitTemplatePathKey, expectedPath)

	// 调用被测方法
	actualPath := GetTokenRateLimitTemplatePath()

	// 验证结果
	if actualPath != expectedPath {
		t.Errorf("Expected %s, but got %s", expectedPath, actualPath)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetKeyAuthPluginName 是用于测试 GetKeyAuthPluginName
// generated by Comate
func TestGetKeyAuthPluginName(t *testing.T) {
	// 设置测试用例
	expectedName := "testPlugin"
	viper.Set(keyAuthPluginNameKey, expectedName)

	// 调用被测方法
	actualName := GetKeyAuthPluginName()

	// 断言结果
	if actualName != expectedName {
		t.Errorf("Expected %s, but got %s", expectedName, actualName)
	}

	// 清理测试用例
	viper.Reset()
}

// TestGetKeyAuthPluginURL 是用于测试 GetKeyAuthPluginURL
// generated by Comate
func TestGetKeyAuthPluginURL(t *testing.T) {
	// 设置测试用例
	expectedURL := "http://example.com/keyauthplugin"
	viper.Set(keyAuthPluginURLKey, expectedURL)

	// 调用被测方法
	actualURL := GetKeyAuthPluginURL()

	// 验证结果
	if actualURL != expectedURL {
		t.Errorf("Expected %s, but got %s", expectedURL, actualURL)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetKeyAuthPluginPriority 是用于测试 GetKeyAuthPluginPriority
// generated by Comate
func TestGetKeyAuthPluginPriority(t *testing.T) {
	// 调用被测方法
	priority := GetKeyAuthPluginPriority()

	// 验证返回值是否与常量KeyAuthPluginPriority相等
	if priority != KeyAuthPluginPriority {
		t.Errorf("Expected priority %d, but got %d", KeyAuthPluginPriority, priority)
	}
}

// TestGetVirtualDenyAllConsumerName 是用于测试 GetVirtualDenyAllConsumerName
// generated by Comate
func TestGetVirtualDenyAllConsumerName(t *testing.T) {
	// 调用被测方法
	result := GetVirtualDenyAllConsumerName()

	// 验证结果
	expected := VirtualDenyAllConsumerName
	if result != expected {
		t.Errorf("Expected %s, but got %s", expected, result)
	}
}

// TestGetKeyAuthTemplatePath 是用于测试 GetKeyAuthTemplatePath
// generated by Comate
func TestGetKeyAuthTemplatePath(t *testing.T) {
	// 设置测试用例
	expectedPath := "/path/to/template"
	viper.Set(keyAuthTemplatePathKey, expectedPath)

	// 调用被测方法
	actualPath := GetKeyAuthTemplatePath()

	// 验证结果
	if actualPath != expectedPath {
		t.Errorf("Expected %s, but got %s", expectedPath, actualPath)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetAIQuotaPluginName 是用于测试 GetAIQuotaPluginName
// generated by Comate
func TestGetAIQuotaPluginName(t *testing.T) {
	// 设置测试用例
	expectedName := "testPlugin"
	viper.Set(aiQuotaPluginNameKey, expectedName)

	// 调用被测方法
	actualName := GetAIQuotaPluginName()

	// 断言结果
	if actualName != expectedName {
		t.Errorf("Expected %s, but got %s", expectedName, actualName)
	}

	// 清理测试用例
	viper.Reset()
}

// TestGetAIQuotaPluginPriority 是用于测试 GetAIQuotaPluginPriority
// generated by Comate
func TestGetAIQuotaPluginPriority(t *testing.T) {
	// 调用被测方法
	priority := GetAIQuotaPluginPriority()

	// 验证返回值是否与常量AIQuotaPluginPriority一致
	if priority != AIQuotaPluginPriority {
		t.Errorf("Expected priority %d, but got %d", AIQuotaPluginPriority, priority)
	}
}

// TestGetAIQuotaPluginURL 是用于测试 GetAIQuotaPluginURL
// generated by Comate
func TestGetAIQuotaPluginURL(t *testing.T) {
	// 设置测试用例
	expectedURL := "http://example.com/ai-quota-plugin"
	viper.Set(aiQuotaPluginURLKey, expectedURL)

	// 调用被测方法
	actualURL := GetAIQuotaPluginURL()

	// 断言结果
	if actualURL != expectedURL {
		t.Errorf("Expected %s, but got %s", expectedURL, actualURL)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetAIQuotaTemplatePath 是用于测试 GetAIQuotaTemplatePath
// generated by Comate
func TestGetAIQuotaTemplatePath(t *testing.T) {
	// 设置测试用例
	expectedPath := "/path/to/templates"
	viper.Set(aiQuotaTemplatePathKey, expectedPath)

	// 调用被测方法
	actualPath := GetAIQuotaTemplatePath()

	// 断言结果
	if actualPath != expectedPath {
		t.Errorf("Expected path %s, but got %s", expectedPath, actualPath)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetIPRestrictionPluginLabelSelector 是用于测试 GetIPRestrictionPluginLabelSelector
// generated by Comate
func TestGetIPRestrictionPluginLabelSelector(t *testing.T) {
	// 设置测试用例
	expectedSelector := WasmPluginLabelPluginType + "=" + WasmPluginTypeIPRestriction

	// 调用被测方法
	actualSelector := GetIPRestrictionPluginLabelSelector()

	// 断言结果
	if actualSelector != expectedSelector {
		t.Errorf("Expected %s, but got %s", expectedSelector, actualSelector)
	}

	// 清理测试用例
	viper.Reset()
}

// TestGetIpRestrictionPluginURL 是用于测试 GetIpRestrictionPluginURL
// generated by Comate
func TestGetIpRestrictionPluginURL(t *testing.T) {
	// 设置测试用例
	expectedURL := "http://example.com/ip_restriction_plugin"
	viper.Set(ipRestrictionPluginURLKey, expectedURL)

	// 调用被测方法
	result := GetIpRestrictionPluginURL()

	// 验证结果
	if result != expectedURL {
		t.Errorf("Expected %s, but got %s", expectedURL, result)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetIpRestrictionTemplatePath 是用于测试 GetIpRestrictionTemplatePath
// generated by Comate
func TestGetIpRestrictionTemplatePath(t *testing.T) {
	// 设置测试数据
	expectedPath := "/path/to/template"
	viper.Set(ipRestrictionTemplatePathKey, expectedPath)

	// 调用被测方法
	actualPath := GetIpRestrictionTemplatePath()

	// 验证结果
	if actualPath != expectedPath {
		t.Errorf("Expected %s, but got %s", expectedPath, actualPath)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetVpcHostingID 是用于测试 GetVpcHostingID
// generated by Comate
func TestGetVpcHostingID(t *testing.T) {
	// 设置测试用例
	expected := "test-vpc-id"
	viper.Set(vpcIDKey, expected)

	// 调用被测方法
	result := GetVpcHostingID()

	// 验证结果
	if result != expected {
		t.Errorf("Expected %s, but got %s", expected, result)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetAIStatisticsPluginName 是用于测试 GetAIStatisticsPluginName
// generated by Comate
func TestGetAIStatisticsPluginName(t *testing.T) {
	// 设置测试数据
	expectedName := "test_ai_statistics_plugin"
	viper.Set(aiStatisticsPluginNameKey, expectedName)

	// 调用被测方法
	actualName := GetAIStatisticsPluginName()

	// 验证结果
	if actualName != expectedName {
		t.Errorf("Expected %s, but got %s", expectedName, actualName)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetHostingClusterByRegion 是用于测试 GetHostingClusterByRegion
// generated by Comate
func TestGetHostingClusterByRegion(t *testing.T) {
	// 设置默认环境
	viper.Set("cloud.hostingRegion", map[string]interface{}{
		"region1": map[string]interface{}{
			"clusterid": "cluster1",
		},
		"region2": map[string]interface{}{
			"clusterid": "cluster2",
		},
	})

	// 测试默认环境
	clusterID := GetHostingClusterByRegion("region1")
	if clusterID != "cluster1" {
		t.Errorf("Expected clusterID to be %s, but got %s", "cluster1", clusterID)
	}

	clusterID = GetHostingClusterByRegion("region2")
	if clusterID != "cluster2" {
		t.Errorf("Expected clusterID to be %s, but got %s", "cluster2", clusterID)
	}

	// 测试不存在的区域
	clusterID = GetHostingClusterByRegion("region3")
	if clusterID != "" {
		t.Errorf("Expected clusterID to be %s, but got %s", "", clusterID)
	}

	// 测试空的区域
	clusterID = GetHostingClusterByRegion("")
	if clusterID != "" {
		t.Errorf("Expected clusterID to be %s, but got %s", "", clusterID)
	}

	// 清除环境设置
	viper.Set("cloud.hostingRegion", nil)
}

// TestGetAIStatisticsPluginURL 是用于测试 GetAIStatisticsPluginURL
// generated by Comate
func TestGetAIStatisticsPluginURL(t *testing.T) {
	// 设置测试用例
	expectedURL := "http://example.com/ai-statistics-plugin"
	viper.Set(aiStatisticsPluginURLKey, expectedURL)

	// 调用被测方法
	actualURL := GetAIStatisticsPluginURL()

	// 断言结果
	if actualURL != expectedURL {
		t.Errorf("Expected %s, but got %s", expectedURL, actualURL)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetAIStatisticsTemplatePath 是用于测试 GetAIStatisticsTemplatePath
// generated by Comate
func TestGetAIStatisticsTemplatePath(t *testing.T) {
	// 设置测试用例
	expectedPath := "/path/to/template"
	viper.Set(aiStatisticsTemplatePathKey, expectedPath)

	// 调用被测方法
	actualPath := GetAIStatisticsTemplatePath()

	// 验证结果
	if actualPath != expectedPath {
		t.Errorf("Expected %s, but got %s", expectedPath, actualPath)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetIpRestrictionPluginURL 是用于测试 GetIpRestrictionPluginURL
// generated by Comate
func TestGetIpRestrictionPluginURL(t *testing.T) {
	// 设置测试数据
	expectedURL := "http://example.com/ip_restriction_plugin"
	viper.Set(ipRestrictionPluginURLKey, expectedURL)

	// 调用被测方法
	actualURL := GetIpRestrictionPluginURL()

	// 验证结果
	if actualURL != expectedURL {
		t.Errorf("Expected %s, but got %s", expectedURL, actualURL)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetIPRestrictionPluginLabelSelector 是用于测试 GetIPRestrictionPluginLabelSelector
// generated by Comate
func TestGetIPRestrictionPluginLabelSelector(t *testing.T) {
	// 设置测试用例
	expectedSelector := WasmPluginLabelPluginType + "=" + WasmPluginTypeIPRestriction

	// 调用被测方法
	actualSelector := GetIPRestrictionPluginLabelSelector()

	// 断言结果
	if actualSelector != expectedSelector {
		t.Errorf("Expected %s, but got %s", expectedSelector, actualSelector)
	}

	// 清理测试用例
	viper.Reset()
}

// TestGetIpRestrictionTemplatePath 是用于测试 GetIpRestrictionTemplatePath
// generated by Comate
func TestGetIpRestrictionTemplatePath(t *testing.T) {
	// 设置测试用例
	expectedPath := "/path/to/ip_restriction_template"
	viper.Set(ipRestrictionTemplatePathKey, expectedPath)

	// 调用被测方法
	actualPath := GetIpRestrictionTemplatePath()

	// 断言结果
	if actualPath != expectedPath {
		t.Errorf("Expected %s, but got %s", expectedPath, actualPath)
	}

	// 清理测试环境
	viper.Reset()
}

// TestGetVpcHostingID 是用于测试 GetVpcHostingID
// generated by Comate
func TestGetVpcHostingID(t *testing.T) {
	// 设置测试数据
	expectedID := "test_vpc_id"
	viper.Set(vpcIDKey, expectedID)

	// 调用被测方法
	actualID := GetVpcHostingID()

	// 验证结果
	if actualID != expectedID {
		t.Errorf("Expected %s, but got %s", expectedID, actualID)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetBlsTemplatePath 是用于测试 GetBlsTemplatePath
// generated by Comate
func TestGetBlsTemplatePath(t *testing.T) {
	// 设置测试数据
	expectedPath := "/path/to/template"
	viper.Set(blsTemplatePathKey, expectedPath)

	// 调用被测方法
	actualPath := GetBlsTemplatePath()

	// 验证结果
	if actualPath != expectedPath {
		t.Errorf("Expected %s, but got %s", expectedPath, actualPath)
	}

	// 清理测试数据
	viper.Reset()
}

// TestGetBlsTemplatePathWithEmptyConfig 是用于测试 GetBlsTemplatePathWithEmptyConfig
// generated by Comate
func TestGetBlsTemplatePathWithEmptyConfig(t *testing.T) {
	// 设置测试数据
	viper.Set(blsTemplatePathKey, "")

	// 调用被测方法
	actualPath := GetBlsTemplatePath()

	// 断言结果
	if actualPath != "" {
		t.Errorf("Expected template path to be empty, but got %s", actualPath)
	}

	// 清理测试数据
	viper.Reset()
}
