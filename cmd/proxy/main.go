package main

import (
	"errors"
	"log"
	"net/http"
	"os"

	"github.com/labstack/echo/v4"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/registercenter"
)

// SetOwner 创建一个Echo中间件，用于设置IAM用户信息到请求上下文中
// 该中间件从环境变量REGISTRY_OWNER获取用户域ID，并将其设置到CSM上下文中
// 这主要用于公网控制台的实现，因为有些地方会直接调用iam.GetAccount方法
// 返回值: echo.MiddlewareFunc - Echo框架的中间件函数
func SetOwner() echo.MiddlewareFunc {
	return func(h echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := context.NewCsmContext(c)
			cc.Set(iam.ContextIAMUser, &sdkIAM.User{
				Domain: sdkIAM.UserDomain{
					ID: os.Getenv("REGISTRY_OWNER"),
				},
			})
			return h(cc)
		}
	}
}

// main 是程序的入口函数，负责启动MSE代理服务器
// 该函数执行以下主要操作：
// 1. 创建Echo Web框架实例并配置中间件
// 2. 初始化API服务器核心组件，包括注册中心代理服务
// 3. 注册所有的HTTP路由端点，包括服务管理、配置管理、用户管理和认证策略等API
// 4. 启动HTTP服务器监听指定端口（默认43297）
//
// 支持的API功能模块：
// - 服务注册与发现：服务列表、服务实例的增删改查、批量操作
// - 命名空间管理：命名空间的增删改查
// - 配置管理：配置组、配置文件、配置发布的完整生命周期管理
// - 用户管理：用户的增删改查、登录认证
// - 权限策略：认证策略的查询和更新
//
// 环境变量依赖：
// - REGISTRY_DOMAIN: 注册中心域名
// - REGISTRY_PORT: 注册中心端口
// - REGISTRY_RELEASE: 注册中心版本
// - REGISTRY_OWNER: 注册中心所有者
// - MSE_PROXY_PORT: 代理服务器监听端口（可选，默认43297）
func main() {
	e := echo.New()
	e.Use(middleware.Context())
	e.Use(SetOwner()) // 公网控制台的实现有直接调用iam.GetAccount的，这里填充上context
	e.Use(middleware.AccessLog())

	c := core.APIServerCore{
		RegisterCenterService: &registercenter.Proxy{
			Domain:  os.Getenv("REGISTRY_DOMAIN"),
			Port:    os.Getenv("REGISTRY_PORT"),
			Release: os.Getenv("REGISTRY_RELEASE"),
			Owner:   os.Getenv("REGISTRY_OWNER"),
		},
	}

	port := os.Getenv("MSE_PROXY_PORT")
	if port == "" {
		port = "43297"
	}

	// 服务管理相关路由
	// 获取服务列表
	e.GET("/v1/registry/services", server.CsmHandler(c.GetServiceList))
	// 获取指定实例的服务列表
	e.GET("/api/mse/v1/registry/:instanceId/services", server.CsmHandler(c.GetServiceList))

	// 创建服务实例
	e.POST("/v1/registry/service/:serviceId/serviceInstance", server.CsmHandler(c.CreateServiceInstance))
	// 在指定实例中创建服务实例
	e.POST("/api/mse/v1/registry/:instanceId/service/:serviceId/serviceInstance", server.CsmHandler(c.CreateServiceInstance))

	// 更新服务实例
	e.PUT("/v1/registry/service/:serviceId/serviceInstance/:serviceInstanceId", server.CsmHandler(c.UpdateServiceInstance))
	// 更新指定实例中的服务实例
	e.PUT("/api/mse/v1/registry/:instanceId/service/:serviceId/serviceInstance/:serviceInstanceId", server.CsmHandler(c.UpdateServiceInstance))

	// 删除服务实例
	e.DELETE("/v1/registry/service/:serviceId/serviceInstance/:serviceInstanceId", server.CsmHandler(c.DeleteServiceInstance))
	// 删除指定实例中的服务实例
	e.DELETE("/api/mse/v1/registry/:instanceId/service/:serviceId/serviceInstance/:serviceInstanceId", server.CsmHandler(c.DeleteServiceInstance))

	// 获取服务实例列表
	e.GET("/v1/registry/service/:serviceId/serviceInstances", server.CsmHandler(c.GetServiceInstanceList))
	// 获取指定实例中的服务实例列表
	e.GET("/api/mse/v1/registry/:instanceId/service/:serviceId/serviceInstances", server.CsmHandler(c.GetServiceInstanceList))

	// 批量操作服务实例
	e.POST("/v1/registry/service/:serviceId/serviceInstances", server.CsmHandler(c.BatchOperateServiceInstance))
	// 批量操作指定实例中的服务实例
	e.POST("/api/mse/v1/registry/:instanceId/service/:serviceId/serviceInstances", server.CsmHandler(c.BatchOperateServiceInstance))

	// 命名空间管理相关路由
	// 创建命名空间
	e.POST("/v1/registry/namespaces", server.CsmHandler(c.CreateRegisterNamespaces))
	// 在指定实例中创建命名空间
	e.POST("/api/mse/v1/registry/:instanceId/namespaces", server.CsmHandler(c.CreateRegisterNamespaces))

	// 删除命名空间
	e.DELETE("/v1/registry/namespaces", server.CsmHandler(c.DeleteRegisterNamespaces))
	// 删除指定实例中的命名空间
	e.DELETE("/api/mse/v1/registry/:instanceId/namespaces", server.CsmHandler(c.DeleteRegisterNamespaces))

	// 更新命名空间
	e.PUT("/v1/registry/namespaces", server.CsmHandler(c.UpdateRegisterNamespaces))
	// 更新指定实例中的命名空间
	e.PUT("/api/mse/v1/registry/:instanceId/namespaces", server.CsmHandler(c.UpdateRegisterNamespaces))

	// 获取命名空间列表
	e.GET("/v1/registry/namespaces", server.CsmHandler(c.GetRegisterNamespaces))
	// 获取指定实例中的命名空间列表
	e.GET("/api/mse/v1/registry/:instanceId/namespaces", server.CsmHandler(c.GetRegisterNamespaces))

	// 配置组管理相关路由
	// 获取配置组列表
	e.GET("/v1/registry/configGroups", server.CsmHandler(c.ListConfigGroup))
	// 获取指定实例的配置组列表
	e.GET("/api/mse/v1/registry/:instanceId/configGroups", server.CsmHandler(c.ListConfigGroup))

	// 获取配置监听器列表
	e.GET("/v1/registry/configListeners", server.CsmHandler(c.ListConfigListener))
	// 获取指定实例的配置监听器列表
	e.GET("/api/mse/v1/registry/:instanceId/configListeners", server.CsmHandler(c.ListConfigListener))

	// 创建配置组
	e.POST("/v1/registry/configGroups", server.CsmHandler(c.CreateConfigGroup))
	// 在指定实例中创建配置组
	e.POST("/api/mse/v1/registry/:instanceId/configGroups", server.CsmHandler(c.CreateConfigGroup))

	// 更新配置组
	e.PUT("/v1/registry/configGroups", server.CsmHandler(c.UpdateConfigGroup))
	// 更新指定实例中的配置组
	e.PUT("/api/mse/v1/registry/:instanceId/configGroups", server.CsmHandler(c.UpdateConfigGroup))

	// 删除配置组
	e.DELETE("/v1/registry/configGroups", server.CsmHandler(c.DeleteConfigGroup))
	// 删除指定实例中的配置组
	e.DELETE("/api/mse/v1/registry/:instanceId/configGroups", server.CsmHandler(c.DeleteConfigGroup))

	// 配置文件管理相关路由
	// 导入配置文件归档
	e.POST("/v1/registry/configFilesArchive", server.CsmHandler(c.ImportConfigFile))
	// 导入指定实例的配置文件归档
	e.POST("/api/mse/v1/registry/:instanceId/configFilesArchive", server.CsmHandler(c.ImportConfigFile))

	// 导出配置文件归档
	e.GET("/v1/registry/configFilesArchive", server.CsmHandler(c.ExportConfigFile))

	// 获取配置文件列表
	e.GET("/v1/registry/configFiles", server.CsmHandler(c.ListConfigFile))
	// 获取指定实例的配置文件列表
	e.GET("/api/mse/v1/registry/:instanceId/configFiles", server.CsmHandler(c.ListConfigFile))

	// 获取配置文件详情
	e.GET("/v1/registry/configFile", server.CsmHandler(c.GetConfigFile))
	// 获取指定实例的配置文件详情
	e.GET("/api/mse/v1/registry/:instanceId/configFile", server.CsmHandler(c.GetConfigFile))

	// 创建配置文件
	e.POST("/v1/registry/configFiles", server.CsmHandler(c.CreateConfigFile))
	// 在指定实例中创建配置文件
	e.POST("/api/mse/v1/registry/:instanceId/configFiles", server.CsmHandler(c.CreateConfigFile))

	// 更新配置文件
	e.PUT("/v1/registry/configFiles", server.CsmHandler(c.UpdateConfigFile))
	// 更新指定实例中的配置文件
	e.PUT("/api/mse/v1/registry/:instanceId/configFiles", server.CsmHandler(c.UpdateConfigFile))

	// 批量操作配置文件
	e.POST("/v1/registry/configFiles/batch", server.CsmHandler(c.BatchConfigFile))
	// 批量操作指定实例中的配置文件
	e.POST("/api/mse/v1/registry/:instanceId/configFiles/batch", server.CsmHandler(c.BatchConfigFile))

	// 配置发布管理相关路由
	// 获取配置发布列表
	e.GET("/v1/registry/configReleases", server.CsmHandler(c.ListConfigRelease))
	// 获取指定实例的配置发布列表
	e.GET("/api/mse/v1/registry/:instanceId/configReleases", server.CsmHandler(c.ListConfigRelease))

	// 获取配置发布详情
	e.GET("/v1/registry/configRelease", server.CsmHandler(c.GetConfigRelease))
	// 获取指定实例的配置发布详情
	e.GET("/api/mse/v1/registry/:instanceId/configRelease", server.CsmHandler(c.GetConfigRelease))

	// 获取配置发布记录列表
	e.GET("/v1/registry/configReleaseRecords", server.CsmHandler(c.ListConfigReleaseRecord))
	// 获取指定实例的配置发布记录列表
	e.GET("/api/mse/v1/registry/:instanceId/configReleaseRecords", server.CsmHandler(c.ListConfigReleaseRecord))

	// 创建配置发布
	e.POST("/v1/registry/configReleases", server.CsmHandler(c.CreateConfigRelease))
	// 在指定实例中创建配置发布
	e.POST("/api/mse/v1/registry/:instanceId/configReleases", server.CsmHandler(c.CreateConfigRelease))

	// 回滚配置发布
	e.PUT("/v1/registry/configReleases", server.CsmHandler(c.RollbackConfigRelease))
	// 回滚指定实例中的配置发布
	e.PUT("/api/mse/v1/registry/:instanceId/configReleases", server.CsmHandler(c.RollbackConfigRelease))

	// 删除配置发布
	e.DELETE("/v1/registry/configReleases", server.CsmHandler(c.DeleteConfigRelease))
	// 删除指定实例中的配置发布
	e.DELETE("/api/mse/v1/registry/:instanceId/configReleases", server.CsmHandler(c.DeleteConfigRelease))

	// 用户管理相关路由
	// 用户登录
	e.POST("/v1/registry/login", server.CsmHandler(c.UserLogin))
	// 用户登录（简化路径）
	e.POST("/login", server.CsmHandler(c.UserLogin))

	// 获取用户列表
	e.GET("/v1/registry/users", server.CsmHandler(c.ListUser))
	// 获取用户列表（简化路径）
	e.GET("/users", server.CsmHandler(c.ListUser))

	// 创建用户
	e.POST("/v1/registry/users", server.CsmHandler(c.CreateUser))
	// 创建用户（简化路径）
	e.POST("/users", server.CsmHandler(c.CreateUser))

	// 删除用户
	e.DELETE("/v1/registry/users", server.CsmHandler(c.DeleteUser))
	// 删除用户（简化路径）
	e.DELETE("/users", server.CsmHandler(c.DeleteUser))

	// 更新用户
	e.PUT("/v1/registry/users", server.CsmHandler(c.UpdateUser))
	// 更新用户（简化路径）
	e.PUT("/users", server.CsmHandler(c.UpdateUser))

	// 重置用户Token
	e.PUT("/v1/registry/user/token/refresh", server.CsmHandler(c.RefreshUserToken))
	// 重置用户Token（简化路径）
	e.PUT("/user/token/refresh", server.CsmHandler(c.RefreshUserToken))

	// 认证策略管理相关路由
	// 获取认证策略列表
	e.GET("/v1/registry/auth/strategies", server.CsmHandler(c.ListAuthStrategy))
	// 获取认证策略列表（简化路径）
	e.GET("/auth/strategies", server.CsmHandler(c.ListAuthStrategy))

	// 更新认证策略
	e.PUT("/v1/registry/auth/strategies", server.CsmHandler(c.UpdateAuthStrategy))
	// 更新认证策略（简化路径）
	e.PUT("/auth/strategies", server.CsmHandler(c.UpdateAuthStrategy))

	if err := e.Start(":" + port); err != nil && !errors.Is(err, http.ErrServerClosed) {
		log.Fatalln(err)
	}
}
