# Polaris 客户端鉴权功能 QA 测试指南

## 📋 测试指南概述

**目标读者**: QA测试工程师  
**测试目标**: 验证Polaris客户端鉴权功能（clientOpen开关）的正确性  
**测试工具**: Nacos Java SDK  
**预计测试时间**: 30-60分钟  

## 🎯 测试目标

验证以下核心功能：
1. **鉴权开关控制**: clientOpen参数能够正确控制鉴权开启/关闭
2. **权限级别控制**: 不同权限级别的访问控制是否生效
3. **操作权限验证**: 读写操作权限是否按预期工作
4. **跨阶段一致性**: 对已存在资源的权限控制是否一致

## 🏗️ 测试环境准备

### 前置条件
- [x] 已部署Polaris实例
- [x] 已创建测试用户和权限配置
- [x] 已安装Java 8+和Maven 3.6+
- [x] 具备kubectl访问权限

### 环境信息获取
在开始测试前，需要获取以下信息：

```bash
# 1. 获取Polaris服务端地址
kubectl get svc -n <namespace> | grep registry

# 2. 获取测试用户Token
# 从管理员处获取或通过控制台获取

# 3. 确认权限配置
# 确保测试用户在不同命名空间有不同权限级别
```

### 测试项目准备
```bash
# 克隆或下载测试项目
cd test/nacos-java-sdk-test

# 编译项目
mvn clean compile

# 验证依赖
mvn dependency:tree | grep nacos
```

## 📝 测试配置

### 1. 配置测试参数
编辑 `src/main/java/com/polaris/test/TestUserConfig.java`：

```java
public class TestUserConfig {
    // 服务端地址 - 需要根据实际环境修改
    public static final String SERVER_ADDR = "your-polaris-server:8848";
    
    // 测试用户信息 - 需要根据实际用户修改
    public static final String USERNAME = "test-user";
    public static final String USER_ID = "your-user-id";
    public static final String TOKEN = "your-user-token";
    
    // 测试命名空间 - 需要根据实际权限配置修改
    public static final String NAMESPACE_WR_AUTH = "wr-auth";     // 读写权限
    public static final String NAMESPACE_READ_ONLY = "read-only"; // 只读权限  
    public static final String NAMESPACE_NO_AUTH = "noauth";      // 无权限
}
```

### 2. 验证权限配置
确保测试用户在三个命名空间有正确的权限配置：

| 命名空间 | 权限级别 | 预期行为 |
|---------|---------|---------|
| wr-auth | 读写权限 | 所有操作成功 |
| read-only | 只读权限 | 只有读操作成功，写操作失败 |
| noauth | 无权限 | 所有操作失败 |

## 🧪 测试执行步骤

### 阶段一：鉴权关闭状态测试

#### 1. 关闭客户端鉴权
```bash
# 设置clientOpen=false
kubectl patch registry <registry-name> -n <namespace> \
  --type='merge' -p='{"spec":{"args":{"clientOpen":false}}}'

# 重启服务使配置生效
kubectl rollout restart statefulset/registry-server -n <namespace>

# 等待重启完成
kubectl rollout status statefulset/registry-server -n <namespace>
```

#### 2. 执行测试
```bash
cd test/nacos-java-sdk-test

# 执行鉴权关闭状态测试
java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
  com.polaris.test.ComprehensiveAuthTest auth-off
```

#### 3. 验证测试结果
**预期结果**: 所有操作都应该成功，不受权限限制

检查点：
- [x] 所有命名空间的服务注册都成功
- [x] 所有命名空间的配置发布都成功
- [x] 所有命名空间的读操作都成功
- [x] 所有命名空间的写操作都成功
- [x] 测试总数48个，全部通过

### 阶段二：鉴权开启状态测试

#### 1. 开启客户端鉴权
```bash
# 清理本地缓存（重要！）
rm -rf ~/nacos/config ~/nacos/data ~/.nacos 2>/dev/null || true

# 设置clientOpen=true
kubectl patch registry <registry-name> -n <namespace> \
  --type='merge' -p='{"spec":{"args":{"clientOpen":true}}}'

# 重启服务使配置生效
kubectl rollout restart statefulset/registry-server -n <namespace>

# 等待重启完成
kubectl rollout status statefulset/registry-server -n <namespace>
```

#### 2. 执行测试
```bash
# 执行鉴权开启状态测试
java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
  com.polaris.test.ComprehensiveAuthTest auth-on
```

#### 3. 验证测试结果
**预期结果**: 根据权限配置控制访问

**wr-auth命名空间（读写权限）**:
- [x] 服务注册：✅ 成功
- [x] 服务查询：✅ 成功  
- [x] 服务删除：✅ 成功
- [x] 配置发布：✅ 成功
- [x] 配置读取：✅ 成功
- [x] 配置修改：✅ 成功
- [x] 配置删除：✅ 成功

**read-only命名空间（只读权限）**:
- [x] 服务注册：❌ 失败（权限不足）
- [x] 服务查询：✅ 成功（包括查询已存在服务）
- [x] 配置发布：❌ 失败（权限不足）
- [x] 配置读取：✅ 成功（包括读取已存在配置）

**noauth命名空间（无权限）**:
- [x] 服务注册：❌ 失败（无权限）
- [x] 服务查询：❌ 失败（无权限）
- [x] 配置发布：❌ 失败（无权限）
- [x] 配置读取：❌ 失败（无权限）

## 🔍 关键测试点验证

### 1. 权限错误信息验证
当操作被拒绝时，应该看到明确的错误信息：
```
❌ 服务实例注册异常:
  - 异常类型: NacosException
  - 异常信息: access is not approved: no permission
```

### 2. 跨阶段资源访问验证
这是本次测试的重点改进：
- **read-only命名空间**: 应该能够读取在鉴权关闭阶段创建的配置
- **noauth命名空间**: 应该无法访问任何已存在的资源
- **权限一致性**: 对新资源和已存在资源的权限控制应该完全一致

### 3. 操作完整性验证
确保测试覆盖了所有关键操作：
- **服务注册中心**: 注册、查询、删除
- **配置中心**: 发布、读取、修改、删除

## 📊 测试结果判定

### 成功标准
- **鉴权关闭阶段**: 48个测试全部通过，成功率100%
- **鉴权开启阶段**: 22个测试全部通过，成功率100%
- **总体成功率**: 70个测试全部通过，成功率100%

### 失败场景处理
如果测试失败，按以下步骤排查：

1. **配置检查**:
   ```bash
   # 检查registry配置
   kubectl get registry <registry-name> -n <namespace> -o yaml | grep clientOpen
   
   # 检查服务状态
   kubectl get pods -n <namespace> | grep registry
   ```

2. **权限检查**:
   - 确认测试用户在各命名空间的权限配置
   - 确认Token是否正确和有效

3. **网络检查**:
   ```bash
   # 测试网络连通性
   telnet <server-addr> 8848
   ```

4. **日志检查**:
   ```bash
   # 查看服务端日志
   kubectl logs -f statefulset/registry-server -n <namespace>
   
   # 查看测试日志
   cat logs/nacos-permission-test-detailed.log
   ```

## 🚨 常见问题和解决方案

### 问题1: 连接超时
**现象**: 无法连接到Polaris服务端
**解决**: 
- 检查服务端地址和端口
- 确认网络连通性
- 检查防火墙设置

### 问题2: Token认证失败
**现象**: 提示认证失败或Token无效
**解决**:
- 确认Token格式正确
- 检查Token是否过期
- 确认用户名和Token匹配

### 问题3: 权限配置不生效
**现象**: 权限控制与预期不符
**解决**:
- 确认clientOpen配置已生效
- 检查服务是否已重启
- 清理客户端本地缓存

### 问题4: 跨阶段测试失败
**现象**: 无法访问已存在资源
**解决**:
- 确认test-resources.json文件存在
- 检查资源是否在鉴权关闭阶段成功创建
- 验证JSON文件内容格式正确

## 📋 测试报告模板

测试完成后，请按以下模板记录测试结果：

```markdown
# 客户端鉴权功能测试报告

## 测试信息
- 测试时间: YYYY-MM-DD HH:MM:SS
- 测试人员: [姓名]
- 测试环境: [环境描述]
- Polaris版本: [版本号]

## 测试结果
- 鉴权关闭阶段: [通过数]/48, 成功率: [百分比]
- 鉴权开启阶段: [通过数]/22, 成功率: [百分比]
- 总体结果: [通过数]/70, 成功率: [百分比]

## 问题记录
[记录测试过程中发现的问题]

## 结论
[测试结论和建议]
```

## 🎯 测试完成检查清单

测试完成前，请确认以下检查项：

- [ ] 环境配置正确
- [ ] 权限配置验证完成
- [ ] 鉴权关闭状态测试通过
- [ ] 鉴权开启状态测试通过
- [ ] 跨阶段资源访问验证通过
- [ ] 错误信息验证正确
- [ ] 测试日志已保存
- [ ] 测试报告已生成
- [ ] 环境已清理（如需要）

## 🛠️ 测试脚本和工具

### 快速测试脚本
为方便QA同学执行，提供以下测试脚本：

#### 1. 环境检查脚本 (check-env.sh)
```bash
#!/bin/bash
echo "=== Polaris 客户端鉴权测试环境检查 ==="

# 检查Java环境
echo "1. 检查Java版本..."
java -version

# 检查Maven环境
echo "2. 检查Maven版本..."
mvn -version

# 检查kubectl连接
echo "3. 检查kubectl连接..."
kubectl cluster-info

# 检查Polaris服务状态
echo "4. 检查Polaris服务状态..."
kubectl get pods -n mse-zdu1rgey | grep registry

# 检查项目编译
echo "5. 检查项目编译..."
cd test/nacos-java-sdk-test
mvn clean compile -q

echo "=== 环境检查完成 ==="
```

#### 2. 完整测试脚本 (run-auth-test.sh)
```bash
#!/bin/bash
set -e

NAMESPACE="mse-zdu1rgey"
REGISTRY_NAME="mse-zdu1rgey"

echo "=== Polaris 客户端鉴权功能完整测试 ==="

# 进入测试目录
cd test/nacos-java-sdk-test

# 编译项目
echo "编译测试项目..."
mvn clean compile -q

# 阶段1: 鉴权关闭测试
echo "=== 阶段1: 鉴权关闭状态测试 ==="
echo "关闭客户端鉴权..."
kubectl patch registry $REGISTRY_NAME -n $NAMESPACE \
  --type='merge' -p='{"spec":{"args":{"clientOpen":false}}}'

echo "重启服务..."
kubectl rollout restart statefulset/registry-server -n $NAMESPACE
kubectl rollout status statefulset/registry-server -n $NAMESPACE --timeout=120s

echo "执行鉴权关闭状态测试..."
java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
  com.polaris.test.ComprehensiveAuthTest auth-off

# 阶段2: 鉴权开启测试
echo "=== 阶段2: 鉴权开启状态测试 ==="
echo "清理本地缓存..."
rm -rf ~/nacos/config ~/nacos/data ~/.nacos 2>/dev/null || true

echo "开启客户端鉴权..."
kubectl patch registry $REGISTRY_NAME -n $NAMESPACE \
  --type='merge' -p='{"spec":{"args":{"clientOpen":true}}}'

echo "重启服务..."
kubectl rollout restart statefulset/registry-server -n $NAMESPACE
kubectl rollout status statefulset/registry-server -n $NAMESPACE --timeout=120s

echo "执行鉴权开启状态测试..."
java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
  com.polaris.test.ComprehensiveAuthTest auth-on

echo "=== 测试完成 ==="
echo "请查看生成的测试报告和日志文件"
```

#### 3. 权限验证脚本 (verify-permissions.sh)
```bash
#!/bin/bash
echo "=== 权限配置验证 ==="

# 这里需要根据实际的权限查询方式来实现
# 示例：通过API查询用户权限
USER_ID="41c8ec3a3ab640378fe9434bbdaf4198"
SERVER_ADDR="180.76.109.137:8848"

echo "验证用户 $USER_ID 的权限配置..."
echo "命名空间 wr-auth: 应该有读写权限"
echo "命名空间 read-only: 应该有只读权限"
echo "命名空间 noauth: 应该无权限"

# 可以添加具体的权限查询逻辑
echo "请手动确认权限配置正确后继续测试"
```

### 测试结果分析工具

#### 日志分析脚本 (analyze-logs.sh)
```bash
#!/bin/bash
echo "=== 测试日志分析 ==="

LOG_DIR="test/nacos-java-sdk-test/logs"

if [ -d "$LOG_DIR" ]; then
    echo "分析测试日志..."

    # 统计成功/失败次数
    echo "成功操作统计:"
    grep -c "✅" $LOG_DIR/test-summary.log || echo "0"

    echo "失败操作统计:"
    grep -c "❌" $LOG_DIR/test-summary.log || echo "0"

    # 显示权限错误
    echo "权限错误信息:"
    grep "no permission" $LOG_DIR/nacos-permission-test-detailed.log || echo "无权限错误"

    # 显示异常信息
    echo "异常信息:"
    grep "Exception" $LOG_DIR/nacos-permission-test-detailed.log || echo "无异常"

else
    echo "日志目录不存在，请先运行测试"
fi
```

## 📱 测试工具使用说明

### 1. 使用测试脚本
```bash
# 给脚本执行权限
chmod +x *.sh

# 执行环境检查
./check-env.sh

# 执行完整测试
./run-auth-test.sh

# 分析测试结果
./analyze-logs.sh
```

### 2. 手动执行单个测试
```bash
# 只测试某个命名空间
java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
  com.polaris.test.ClientAuthTestUtil test-namespace wr-auth

# 只测试服务注册功能
java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
  com.polaris.test.ClientAuthTestUtil test-service-registration wr-auth
```

### 3. 调试模式
```bash
# 启用详细日志
export NACOS_CLIENT_LOG_LEVEL=DEBUG

# 执行测试
java -Dnacos.logging.default.config.enabled=false \
  -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
  com.polaris.test.ComprehensiveAuthTest auth-on
```

## 🔧 故障排除指南

### 快速诊断命令
```bash
# 检查Polaris服务状态
kubectl get pods -n mse-zdu1rgey -l app=registry-server

# 检查配置是否生效
kubectl get registry mse-zdu1rgey -n mse-zdu1rgey -o jsonpath='{.spec.args.clientOpen}'

# 查看服务日志
kubectl logs -f deployment/registry-server -n mse-zdu1rgey --tail=100

# 检查网络连通性
nc -zv 180.76.109.137 8848
```

### 常用重置命令
```bash
# 重置客户端缓存
rm -rf ~/nacos/config ~/nacos/data ~/.nacos

# 重启Polaris服务
kubectl rollout restart statefulset/registry-server -n mse-zdu1rgey

# 清理测试资源
rm -f test/nacos-java-sdk-test/test-resources.json
rm -rf test/nacos-java-sdk-test/logs/*
```

---

**注意**: 本测试指南基于实际测试验证，如遇到问题请及时反馈给开发团队。测试脚本需要根据实际环境调整相关参数。
