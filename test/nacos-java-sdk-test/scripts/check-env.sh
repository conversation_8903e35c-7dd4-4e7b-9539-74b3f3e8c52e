#!/bin/bash

# Polaris 客户端鉴权测试环境检查脚本
# 用途: 检查测试环境是否就绪
# 作者: Polaris Team
# 版本: 1.0

set -e

echo "=================================================================="
echo "           Polaris 客户端鉴权测试环境检查"
echo "=================================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 已安装${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 未安装${NC}"
        return 1
    fi
}

check_java() {
    echo "1. 检查Java环境..."
    if check_command java; then
        java -version 2>&1 | head -1
        JAVA_VERSION=$(java -version 2>&1 | head -1 | cut -d'"' -f2 | cut -d'.' -f1-2)
        if [[ "$JAVA_VERSION" > "1.7" ]]; then
            echo -e "${GREEN}✅ Java版本满足要求${NC}"
        else
            echo -e "${RED}❌ Java版本过低，需要Java 8+${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ 请安装Java 8+${NC}"
        return 1
    fi
}

check_maven() {
    echo "2. 检查Maven环境..."
    if check_command mvn; then
        mvn -version | head -1
        echo -e "${GREEN}✅ Maven环境正常${NC}"
    else
        echo -e "${RED}❌ 请安装Maven 3.6+${NC}"
        return 1
    fi
}

check_kubectl() {
    echo "3. 检查kubectl连接..."
    if check_command kubectl; then
        if kubectl cluster-info &> /dev/null; then
            echo -e "${GREEN}✅ kubectl连接正常${NC}"
            kubectl cluster-info | head -1
        else
            echo -e "${RED}❌ kubectl无法连接到集群${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ 请安装kubectl${NC}"
        return 1
    fi
}

check_polaris_service() {
    echo "4. 检查Polaris服务状态..."
    NAMESPACE=${POLARIS_NAMESPACE:-"mse-zdu1rgey"}
    
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        echo -e "${GREEN}✅ 命名空间 $NAMESPACE 存在${NC}"
        
        # 检查registry服务
        REGISTRY_PODS=$(kubectl get pods -n $NAMESPACE -l app=registry-server --no-headers 2>/dev/null | wc -l)
        if [ $REGISTRY_PODS -gt 0 ]; then
            echo -e "${GREEN}✅ 找到 $REGISTRY_PODS 个registry服务实例${NC}"
            
            # 检查服务状态
            READY_PODS=$(kubectl get pods -n $NAMESPACE -l app=registry-server --no-headers 2>/dev/null | grep "Running" | wc -l)
            if [ $READY_PODS -gt 0 ]; then
                echo -e "${GREEN}✅ $READY_PODS 个服务实例运行正常${NC}"
            else
                echo -e "${YELLOW}⚠️  服务实例未就绪，请检查pod状态${NC}"
                kubectl get pods -n $NAMESPACE -l app=registry-server
            fi
        else
            echo -e "${RED}❌ 未找到registry服务实例${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ 命名空间 $NAMESPACE 不存在${NC}"
        return 1
    fi
}

check_project() {
    echo "5. 检查测试项目..."
    
    # 检查项目目录
    if [ -d "test/nacos-java-sdk-test" ]; then
        echo -e "${GREEN}✅ 测试项目目录存在${NC}"
        cd test/nacos-java-sdk-test
        
        # 检查pom.xml
        if [ -f "pom.xml" ]; then
            echo -e "${GREEN}✅ Maven项目配置存在${NC}"
            
            # 尝试编译
            echo "正在编译项目..."
            if mvn clean compile -q; then
                echo -e "${GREEN}✅ 项目编译成功${NC}"
            else
                echo -e "${RED}❌ 项目编译失败${NC}"
                return 1
            fi
        else
            echo -e "${RED}❌ 未找到pom.xml文件${NC}"
            return 1
        fi
        
        cd - > /dev/null
    else
        echo -e "${RED}❌ 测试项目目录不存在${NC}"
        echo "请确保在正确的工作目录下运行此脚本"
        return 1
    fi
}

check_network() {
    echo "6. 检查网络连通性..."
    SERVER_ADDR=${POLARIS_SERVER_ADDR:-"**************"}
    SERVER_PORT=${POLARIS_SERVER_PORT:-"8848"}
    
    if command -v nc &> /dev/null; then
        if nc -z $SERVER_ADDR $SERVER_PORT 2>/dev/null; then
            echo -e "${GREEN}✅ 可以连接到 $SERVER_ADDR:$SERVER_PORT${NC}"
        else
            echo -e "${YELLOW}⚠️  无法连接到 $SERVER_ADDR:$SERVER_PORT${NC}"
            echo "请检查网络连接或服务地址配置"
        fi
    else
        echo -e "${YELLOW}⚠️  nc命令不可用，跳过网络连通性检查${NC}"
    fi
}

# 主检查流程
main() {
    local failed=0
    
    check_java || failed=1
    echo
    
    check_maven || failed=1
    echo
    
    check_kubectl || failed=1
    echo
    
    check_polaris_service || failed=1
    echo
    
    check_project || failed=1
    echo
    
    check_network
    echo
    
    echo "=================================================================="
    if [ $failed -eq 0 ]; then
        echo -e "${GREEN}🎉 环境检查通过，可以开始测试！${NC}"
        echo
        echo "下一步："
        echo "1. 配置测试参数（编辑 TestUserConfig.java）"
        echo "2. 运行完整测试：./scripts/run-auth-test.sh"
    else
        echo -e "${RED}❌ 环境检查失败，请解决上述问题后重试${NC}"
        exit 1
    fi
    echo "=================================================================="
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo
    echo "环境变量:"
    echo "  POLARIS_NAMESPACE      Polaris部署的命名空间 (默认: mse-zdu1rgey)"
    echo "  POLARIS_SERVER_ADDR    Polaris服务器地址 (默认: **************)"
    echo "  POLARIS_SERVER_PORT    Polaris服务器端口 (默认: 8848)"
    echo
    echo "示例:"
    echo "  $0                                    # 使用默认配置检查"
    echo "  POLARIS_NAMESPACE=my-ns $0           # 指定命名空间"
    echo "  POLARIS_SERVER_ADDR=************* $0 # 指定服务器地址"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_usage
        exit 0
        ;;
    *)
        main
        ;;
esac
