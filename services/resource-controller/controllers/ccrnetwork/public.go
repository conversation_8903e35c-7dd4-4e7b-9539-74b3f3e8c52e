package ccrnetwork

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/eip"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Public struct {
	clients            clientset.ClientSetInterface
	config             *conf.Config
	k8sclient          client.Client
	eipBillingMethod   string
	resourceSourceName string
}

func NewPublic(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Public {
	var eipBillingMethod string
	if config.EIPBillingMethod != "" {
		eipBillingMethod = config.EIPBillingMethod
	} else {
		eipBillingMethod = "ByTraffic"
	}
	return &Public{
		config:             config,
		clients:            clients,
		k8sclient:          cli,
		eipBillingMethod:   eipBillingMethod,
		resourceSourceName: config.ServiceName,
	}
}

func (handler *Public) handlePublic(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	// check whitelist
	if err := handler.handleWhiteList(ctx, object); err != nil {
		object.Status.PublicStatus.Reason = "whitelistUpdateFailed"
		return fmt.Errorf("update white list failed: %w", err)
	}

	if object.Spec.PublicLink.EIPOn {
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpened {
			// 修复：EIP已开启时，检查是否需要更新配置（如带宽）
			logger := log.FromContext(ctx)
			logger.Info("EIP is opened, checking for configuration updates",
				"instance", object.GetName(),
				"currentStatus", object.Status.PublicStatus.Status)

			return handler.handleEIPConfigurationUpdate(ctx, object)
		}
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusClosed ||
			object.Status.PublicStatus.Status == "" {
			object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusOpening
			return nil
		}
	}

	if !object.Spec.PublicLink.EIPOn {
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusClosed {
			return nil
		}
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpened ||
			object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpening {
			object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusClosing
			return nil
		}
	}

	if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpening {
		// handle eip creating
		return handler.handleCreateEIPAndBinding(ctx, object)
	}

	if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusClosing {
		// handle eip closing
		return handler.handleDeleteAndUnbindEip(ctx, object)
	}

	return nil
}

// handleEIPConfigurationUpdate 处理EIP配置更新（当EIP已开启但配置需要更新时）
func (handler *Public) handleEIPConfigurationUpdate(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	logger.Info("Starting EIP configuration update check",
		"instance", object.GetName(),
		"specBandwidth", object.Spec.PublicLink.BandwidthMbps,
		"currentAddress", object.Status.PublicStatus.Address)

	// 检查是否需要更新EIP配置
	needsUpdate, updateReason := handler.needsEIPConfigurationUpdate(ctx, object)
	if !needsUpdate {
		logger.Info("EIP configuration is up-to-date, no update needed",
			"instance", object.GetName(), "reason", updateReason)
		return nil
	}

	logger.Info("EIP configuration needs update",
		"instance", object.GetName(), "reason", updateReason)

	// 执行EIP配置更新
	return handler.updateEIPConfiguration(ctx, object)
}

// needsEIPConfigurationUpdate 检查是否需要更新EIP配置
func (handler *Public) needsEIPConfigurationUpdate(ctx context.Context, object *ccrv1alpha1.CNCNetwork) (bool, string) {
	logger := log.FromContext(ctx)

	// 检查是否有EIP地址
	if object.Status.PublicStatus.Address == "" {
		logger.Info("No EIP address found, update not needed", "instance", object.GetName())
		return false, "no EIP address"
	}

	// 检查带宽配置是否需要更新
	specBandwidth := object.Spec.PublicLink.BandwidthMbps
	if specBandwidth <= 0 {
		logger.Info("Invalid bandwidth specification, update not needed",
			"instance", object.GetName(), "specBandwidth", specBandwidth)
		return false, "invalid bandwidth specification"
	}

	// 获取当前EIP的实际带宽配置
	currentBandwidth, err := handler.getCurrentEIPBandwidth(ctx, object)
	if err != nil {
		logger.Error(err, "Failed to get current EIP bandwidth", "instance", object.GetName())
		return false, "failed to get current bandwidth"
	}

	logger.Info("EIP bandwidth comparison",
		"instance", object.GetName(),
		"currentBandwidth", currentBandwidth,
		"specBandwidth", specBandwidth)

	if currentBandwidth != specBandwidth {
		return true, fmt.Sprintf("bandwidth mismatch: current=%d, expected=%d", currentBandwidth, specBandwidth)
	}

	return false, "configuration matches"
}

// handleWhiteList 处理白名单（使用安全组实现）
func (handler *Public) handleWhiteList(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	// 只有开启公网访问时才处理安全组
	if object.Spec.PublicLink.EIPOn {
		// 修复：移除过于激进的跳过逻辑，确保配置变化时能够正确更新
		// 依赖handleSecurityGroup内部的幂等性检查来避免重复操作

		// 如果是绑定失败的情况，记录重试信息
		if object.Status.PublicStatus.Reason == "bindSecurityGroupFailed" &&
			object.Status.PublicStatus.SecurityGroupId != "" {
			logger.Info("Retrying security group binding after previous failure",
				"instance", object.GetName(), "sgId", object.Status.PublicStatus.SecurityGroupId)
		}

		return handler.handleSecurityGroup(ctx, object)
	}

	// 关闭公网访问时，清理安全组
	if object.Status.PublicStatus.SecurityGroupId != "" {
		return handler.cleanupSecurityGroup(ctx, object)
	}

	return nil
}

// handleSecurityGroup handles security group whitelist (creates security group regardless of public access)
func (handler *Public) handleSecurityGroup(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	accountId := object.Spec.AccountID
	bccClient, err := handler.clients.BccClientFromAccount(accountId, "", handler.config.BCCEndpoint)
	if err != nil {
		logger.Error(err, "Failed to create BCC client", "instance", object.GetName())
		return fmt.Errorf("create bcc client failed: %w", err)
	}

	vpcClient, err := handler.clients.VpcClientFromAccount(accountId, "", handler.config.VPCEndpoint)
	if err != nil {
		logger.Error(err, "Failed to create VPC client", "instance", object.GetName())
		return fmt.Errorf("create vpc client failed: %w", err)
	}

	sgId, err := handler.ensureSecurityGroup(ctx, bccClient, vpcClient, object)
	if err != nil {
		logger.Error(err, "Failed to ensure security group exists", "instance", object.GetName())
		return fmt.Errorf("ensure security group failed: %w", err)
	}

	// 绑定安全组到服务网卡
	var bindingSuccess bool = true
	if len(object.Status.LinkStatus) > 0 {
		endpointId := object.Status.LinkStatus[0].ServiceID
		err = handler.bindSecurityGroupToEndpoint(ctx, bccClient, sgId, endpointId, object)
		if err != nil {
			logger.Error(err, "Failed to bind security group to service endpoint", "instance", object.GetName(), "endpointId", endpointId)
			object.Status.PublicStatus.Reason = "bindSecurityGroupFailed"
			bindingSuccess = false
		} else {
			if object.Status.PublicStatus.Reason == "bindSecurityGroupFailed" {
				object.Status.PublicStatus.Reason = ""
			}
		}
	}

	// 更新安全组规则（如果白名单配置有变化）
	rulesUpdateSuccess := false
	if handler.shouldUpdateSecurityGroupRules(ctx, object, sgId) {
		logger.Info("Starting security group rules update", "instance", object.GetName(), "sgId", sgId)
		err = handler.updateSecurityGroupRules(ctx, bccClient, vpcClient, sgId, object)
		if err != nil {
			logger.Error(err, "Failed to update security group rules", "instance", object.GetName(), "sgId", sgId)
			// 修复：记录更新失败的状态，但不返回错误避免影响绑定结果
			object.Status.PublicStatus.Reason = "updateSecurityGroupRulesFailed"
			rulesUpdateSuccess = false
		} else {
			logger.Info("Security group rules updated successfully", "instance", object.GetName(), "sgId", sgId)
			// 清除可能的更新失败状态
			if object.Status.PublicStatus.Reason == "updateSecurityGroupRulesFailed" {
				object.Status.PublicStatus.Reason = ""
			}
			rulesUpdateSuccess = true
		}
	} else {
		logger.Info("Security group rules update not needed", "instance", object.GetName(), "sgId", sgId)
		rulesUpdateSuccess = true // 不需要更新也算成功
	}

	// 修复：只有在规则更新成功后才同步状态，避免提前同步导致检查失效
	if rulesUpdateSuccess {
		logger.Info("Syncing white list status after successful rules update", "instance", object.GetName())
		object.Status.PublicStatus.WhiteList = object.Spec.PublicLink.WhiteList
	} else {
		logger.Info("Skipping white list status sync due to rules update failure", "instance", object.GetName())
	}

	// 更新绑定状态
	if bindingSuccess {
		// 只有绑定成功时才清除错误状态
		if object.Status.PublicStatus.Reason == "bindSecurityGroupFailed" {
			object.Status.PublicStatus.Reason = ""
		}
	}

	logger.Info("Security group whitelist processing completed", "instance", object.GetName(), "sgId", sgId)
	return nil
}

// ensureSecurityGroup 确保安全组存在，如果不存在则创建
func (handler *Public) ensureSecurityGroup(ctx context.Context, bccClient *bcc.Client, vpcClient *vpc.Client, object *ccrv1alpha1.CNCNetwork) (string, error) {
	logger := log.FromContext(ctx)

	// 修复：简化检查逻辑，仅依赖本地状态避免API调用失败导致的重复创建
	// 如果状态中已有安全组ID，直接返回，不进行远程验证
	if object.Status.PublicStatus.SecurityGroupId != "" {
		logger.Info("Security group ID exists in status, reusing",
			"instance", object.GetName(), "sgId", object.Status.PublicStatus.SecurityGroupId)
		return object.Status.PublicStatus.SecurityGroupId, nil
	}

	// 只有当状态中没有安全组ID时，才创建新的安全组
	logger.Info("No security group ID in status, creating new security group", "instance", object.GetName())
	sgId, err := handler.createSecurityGroup(ctx, bccClient, vpcClient, object)
	if err != nil {
		return "", err
	}

	// 立即更新状态，避免重复创建
	object.Status.PublicStatus.SecurityGroupId = sgId
	logger.Info("Updated security group ID in status", "instance", object.GetName(), "sgId", sgId)

	return sgId, nil
}

// isSecurityGroupExists 检查安全组是否存在，使用更可靠的方式
func (handler *Public) isSecurityGroupExists(ctx context.Context, bccClient *bcc.Client, sgId string) bool {
	logger := log.FromContext(ctx)

	// 方法1：直接获取安全组详情
	_, err := bccClient.GetSecurityGroupDetail(sgId)
	if err == nil {
		return true
	}

	// 方法2：如果直接获取失败，尝试通过列表查找（更可靠）
	logger.Info("Direct security group query failed, trying list approach", "sgId", sgId, "error", err.Error())

	listArgs := &bcc.ListSecurityGroupArgs{
		MaxKeys: 1000, // 设置足够大的值
	}

	result, err := bccClient.ListSecurityGroup(listArgs)
	if err != nil {
		logger.Error(err, "Failed to list security groups for existence check", "sgId", sgId)
		return false // 如果列表也失败，假设不存在
	}

	// 在列表中查找目标安全组
	for _, sg := range result.SecurityGroups {
		if sg.Id == sgId {
			logger.Info("Security group found in list", "sgId", sgId)
			return true
		}
	}

	logger.Info("Security group not found in list", "sgId", sgId)
	return false
}

// createSecurityGroup 创建安全组并设置默认规则
func (handler *Public) createSecurityGroup(ctx context.Context, bccClient *bcc.Client, vpcClient *vpc.Client, object *ccrv1alpha1.CNCNetwork) (string, error) {
	logger := log.FromContext(ctx)

	// 1. 构建安全组名称
	sgName := fmt.Sprintf("%s-whitelist", object.GetName())

	// 2. 获取VPC CIDR
	vpcId := object.Spec.PrivateLinks[0].VPCID
	vpcDetail, err := vpcClient.GetVPCDetail(vpcId)
	if err != nil {
		return "", fmt.Errorf("get vpc detail failed: %w", err)
	}

	// 3. 构建默认规则
	defaultRules := handler.buildDefaultSecurityGroupRules(vpcDetail.VPC.Cidr, object)

	// 4. 创建安全组
	createArgs := &bcc.CreateSecurityGroupArgs{
		Name:        sgName,
		VpcId:       vpcId,
		Desc:        fmt.Sprintf("MSE实例%s的IP白名单安全组，请勿删除", object.GetName()),
		Rules:       defaultRules,
		ClientToken: string(uuid.NewUUID()),
	}

	result, err := bccClient.CreateSecurityGroup(createArgs)
	if err != nil {
		return "", fmt.Errorf("create security group failed: %w", err)
	}

	logger.Info("Security group created", "instance", object.GetName(), "sgId", result.SecurityGroupId, "sgName", sgName)
	return result.SecurityGroupId, nil
}

// buildDefaultSecurityGroupRules 构建默认的安全组规则
func (handler *Public) buildDefaultSecurityGroupRules(vpcCidr string, object *ccrv1alpha1.CNCNetwork) []bcc.SecurityGroupRuleModel {
	var rules []bcc.SecurityGroupRuleModel

	// 1. 默认允许的内网CIDR
	defaultCIDRs := []string{
		"***********/16", // 私有网络A类
		"10.0.0.0/8",     // 私有网络B类
		"**********/12",  // 私有网络C类
		"**********/10",  // 运营商级NAT
		vpcCidr,          // 当前VPC的CIDR（最重要）
	}

	// 2. 添加用户配置的白名单（去重处理）
	allowedCIDRsMap := make(map[string]bool)
	var allowedCIDRs []string

	// 先添加默认CIDR
	for _, cidr := range defaultCIDRs {
		if !allowedCIDRsMap[cidr] {
			allowedCIDRsMap[cidr] = true
			allowedCIDRs = append(allowedCIDRs, cidr)
		}
	}

	// 再添加用户配置的白名单（去重）
	if object.Spec.PublicLink.EIPOn && len(object.Spec.PublicLink.WhiteList) > 0 {
		for _, item := range object.Spec.PublicLink.WhiteList {
			cidr := handler.formatWhiteListCIDR(item.IPCidr)
			if !allowedCIDRsMap[cidr] {
				allowedCIDRsMap[cidr] = true
				allowedCIDRs = append(allowedCIDRs, cidr)
			}
		}
	}

	// 3. 为每个CIDR创建入站规则（允许所有端口和协议）
	for i, cidr := range allowedCIDRs {
		rule := bcc.SecurityGroupRuleModel{
			Remark:    fmt.Sprintf("MSE白名单规则-%d", i+1),
			Protocol:  "all",     // 使用"all"而不是空字符串
			PortRange: "1-65535", // 使用"1-65535"而不是空字符串
			Direction: "ingress", // 入站规则
			SourceIp:  cidr,
		}
		rules = append(rules, rule)
	}

	// 4. 添加默认出站规则（允许所有出站流量）
	egressRule := bcc.SecurityGroupRuleModel{
		Remark:    "MSE默认出站规则",
		Protocol:  "all",       // 使用"all"而不是空字符串
		PortRange: "1-65535",   // 使用"1-65535"而不是空字符串
		Direction: "egress",    // 出站规则
		DestIp:    "0.0.0.0/0", // 所有目标
	}
	rules = append(rules, egressRule)

	return rules
}

// formatWhiteListCIDR 格式化白名单CIDR
func (handler *Public) formatWhiteListCIDR(ip string) string {
	if !strings.Contains(ip, "/") {
		if ip == "0.0.0.0" {
			return "0.0.0.0/0" // 允许所有IP
		}
		return ip + "/32" // 单个IP
	}
	return ip
}

// shouldUpdateSecurityGroupRules 判断是否需要更新安全组规则
func (handler *Public) shouldUpdateSecurityGroupRules(ctx context.Context, object *ccrv1alpha1.CNCNetwork, sgId string) bool {
	logger := log.FromContext(ctx)

	logger.Info("Checking if security group rules need update",
		"instance", object.GetName(),
		"sgId", sgId,
		"statusSgId", object.Status.PublicStatus.SecurityGroupId,
		"specWhiteListCount", len(object.Spec.PublicLink.WhiteList),
		"statusWhiteListCount", len(object.Status.PublicStatus.WhiteList))

	// 修复：简化判断逻辑，主要检查白名单配置是否有变化
	// 不再依赖安全组ID的匹配，因为新创建的安全组ID可能还没同步到状态

	specWhiteList := object.Spec.PublicLink.WhiteList
	statusWhiteList := object.Status.PublicStatus.WhiteList

	// 详细比较白名单配置
	if len(specWhiteList) != len(statusWhiteList) {
		logger.Info("White list length changed, need update",
			"instance", object.GetName(),
			"specCount", len(specWhiteList),
			"statusCount", len(statusWhiteList))
		return true
	}

	// 详细比较白名单内容
	logger.Info("Detailed white list comparison",
		"instance", object.GetName(),
		"specWhiteList", specWhiteList,
		"statusWhiteList", statusWhiteList)

	// 逐项比较白名单内容
	for i, specItem := range specWhiteList {
		logger.Info("Spec white list item",
			"instance", object.GetName(), "index", i,
			"ipCidr", specItem.IPCidr, "description", specItem.Description)
	}

	for i, statusItem := range statusWhiteList {
		logger.Info("Status white list item",
			"instance", object.GetName(), "index", i,
			"ipCidr", statusItem.IPCidr, "description", statusItem.Description)
	}

	// 深度比较白名单内容
	if !reflect.DeepEqual(specWhiteList, statusWhiteList) {
		logger.Info("White list content changed, need update",
			"instance", object.GetName(),
			"reason", "DeepEqual returned false")
		return true
	}

	logger.Info("White list configuration unchanged, no update needed",
		"instance", object.GetName(),
		"reason", "DeepEqual returned true")
	return false
}

// updateSecurityGroupRules 更新安全组规则（直接更新现有安全组）
func (handler *Public) updateSecurityGroupRules(ctx context.Context, bccClient *bcc.Client, vpcClient *vpc.Client, sgId string, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	logger.Info("Starting security group rules update process",
		"instance", object.GetName(), "sgId", sgId,
		"specWhiteListCount", len(object.Spec.PublicLink.WhiteList))

	// 1. 获取当前安全组详情
	logger.Info("Fetching current security group details", "instance", object.GetName(), "sgId", sgId)
	currentSG, err := bccClient.GetSecurityGroupDetail(sgId)
	if err != nil {
		logger.Error(err, "Failed to get current security group details", "instance", object.GetName(), "sgId", sgId)
		return fmt.Errorf("get current security group failed: %w", err)
	}

	logger.Info("Current security group details retrieved",
		"instance", object.GetName(), "sgId", sgId,
		"currentRulesCount", len(currentSG.Rules),
		"sgName", currentSG.Name,
		"sgVpcId", currentSG.VpcId)

	// 2. 获取VPC CIDR
	vpcId := object.Spec.PrivateLinks[0].VPCID
	logger.Info("Fetching VPC details", "instance", object.GetName(), "vpcId", vpcId)
	vpcDetail, err := vpcClient.GetVPCDetail(vpcId)
	if err != nil {
		logger.Error(err, "Failed to get VPC details", "instance", object.GetName(), "vpcId", vpcId)
		return fmt.Errorf("get vpc detail failed: %w", err)
	}

	logger.Info("VPC details retrieved",
		"instance", object.GetName(), "vpcId", vpcId, "vpcCidr", vpcDetail.VPC.Cidr)

	// 3. 构建新的规则列表
	logger.Info("Building new security group rules", "instance", object.GetName())
	newRules := handler.buildDefaultSecurityGroupRules(vpcDetail.VPC.Cidr, object)

	// 详细记录规则对比信息
	logger.Info("Security group rules comparison",
		"instance", object.GetName(), "sgId", sgId,
		"currentRulesCount", len(currentSG.Rules),
		"newRulesCount", len(newRules),
		"vpcCidr", vpcDetail.VPC.Cidr)

	// 记录当前规则详情
	for i, rule := range currentSG.Rules {
		logger.Info("Current rule",
			"instance", object.GetName(), "ruleIndex", i,
			"direction", rule.Direction, "ruleId", rule.SecurityGroupRuleId,
			"portRange", rule.PortRange, "protocol", rule.Protocol,
			"sourceIp", rule.SourceIp, "destIp", rule.DestIp)
	}

	// 记录新规则详情
	for i, rule := range newRules {
		logger.Info("New rule",
			"instance", object.GetName(), "ruleIndex", i,
			"direction", rule.Direction, "remark", rule.Remark,
			"portRange", rule.PortRange, "protocol", rule.Protocol,
			"sourceIp", rule.SourceIp, "sourceGroupId", rule.SourceGroupId)
	}

	// 4. 计算规则差异并增量更新
	logger.Info("Starting incremental security group rules update", "instance", object.GetName(), "sgId", sgId)
	err = handler.updateSecurityGroupRulesIncremental(ctx, bccClient, sgId, currentSG.Rules, newRules)
	if err != nil {
		logger.Error(err, "Incremental security group rules update failed", "instance", object.GetName(), "sgId", sgId)
		return fmt.Errorf("incremental update security group rules failed: %w", err)
	}

	logger.Info("Security group rules update completed successfully", "instance", object.GetName(), "sgId", sgId)
	return nil
}

// updateSecurityGroupRulesIncremental 增量更新安全组规则
func (handler *Public) updateSecurityGroupRulesIncremental(ctx context.Context, bccClient *bcc.Client, sgId string, currentRules []bcc.SecurityGroupRuleDetail, newRules []bcc.SecurityGroupRuleModel) error {
	logger := log.FromContext(ctx)

	logger.Info("Starting incremental security group rules update",
		"sgId", sgId, "currentRulesCount", len(currentRules), "newRulesCount", len(newRules))

	// 如果当前规则为空，说明是新创建的安全组，已经包含了正确的规则，无需更新
	if len(currentRules) == 0 {
		logger.Info("New security group detected, rules already correct, skipping update", "sgId", sgId)
		return nil
	}

	// 检查是否需要更新规则
	if handler.isRulesEqual(currentRules, newRules) {
		logger.Info("Security group rules are identical, no update needed", "sgId", sgId)
		return nil
	}

	logger.Info("Security group rules differ, proceeding with update",
		"sgId", sgId, "currentRulesCount", len(currentRules), "newRulesCount", len(newRules))

	// 简化实现：先删除所有现有的入站规则，再添加新规则
	logger.Info("Starting rule replacement process: delete existing ingress rules, then add new rules", "sgId", sgId)

	// 1. 撤销所有现有的入站规则（保留出站规则）
	ingressRuleCount := 0
	for _, rule := range currentRules {
		if rule.Direction == "ingress" {
			ingressRuleCount++
		}
	}

	logger.Info("Revoking existing ingress rules", "sgId", sgId, "ingressRuleCount", ingressRuleCount)

	revokedCount := 0
	for i, rule := range currentRules {
		if rule.Direction == "ingress" {
			logger.Info("Revoking ingress rule",
				"sgId", sgId, "ruleIndex", i, "ruleId", rule.SecurityGroupRuleId,
				"protocol", rule.Protocol, "portRange", rule.PortRange, "sourceIp", rule.SourceIp)

			revokeArgs := &bcc.RevokeSecurityGroupArgs{
				Rule: bcc.SecurityGroupRuleModel{
					Remark:        rule.Remark, // 必须包含Remark字段
					Direction:     rule.Direction,
					Protocol:      rule.Protocol,
					PortRange:     rule.PortRange,
					SourceIp:      rule.SourceIp,
					SourceGroupId: "", // SecurityGroupRuleDetail没有SourceGroupId字段
					DestIp:        rule.DestIp,
					DestGroupId:   rule.DestGroupId,
				},
			}

			err := bccClient.RevokeSecurityGroupRule(sgId, revokeArgs)
			if err != nil {
				// 如果规则不存在，忽略错误（可能已经被删除）
				if !strings.Contains(err.Error(), "SecurityGroup.RuleNotExist") {
					logger.Error(err, "Failed to revoke security group rule",
						"sgId", sgId, "ruleId", rule.SecurityGroupRuleId, "sourceIp", rule.SourceIp)
				} else {
					logger.Info("Rule already removed, ignoring",
						"sgId", sgId, "ruleId", rule.SecurityGroupRuleId)
				}
				// 继续处理其他规则，不中断流程
			} else {
				revokedCount++
				logger.Info("Successfully revoked ingress rule",
					"sgId", sgId, "ruleId", rule.SecurityGroupRuleId, "sourceIp", rule.SourceIp)
			}
		}
	}

	logger.Info("Completed revoking ingress rules", "sgId", sgId, "revokedCount", revokedCount, "totalIngressRules", ingressRuleCount)

	// 2. 添加新的入站规则
	newIngressRuleCount := 0
	for _, rule := range newRules {
		if rule.Direction == "ingress" {
			newIngressRuleCount++
		}
	}

	logger.Info("Adding new ingress rules", "sgId", sgId, "newIngressRuleCount", newIngressRuleCount)

	addedCount := 0
	for i, rule := range newRules {
		if rule.Direction == "ingress" {
			logger.Info("Adding new ingress rule",
				"sgId", sgId, "ruleIndex", i,
				"protocol", rule.Protocol, "portRange", rule.PortRange, "sourceIp", rule.SourceIp)

			authorizeArgs := &bcc.AuthorizeSecurityGroupArgs{
				ClientToken: string(uuid.NewUUID()),
				Rule:        rule,
			}

			err := bccClient.AuthorizeSecurityGroupRule(sgId, authorizeArgs)
			if err != nil {
				// 如果规则已存在，忽略错误
				if !strings.Contains(err.Error(), "SecurityGroup.RuleDuplicated") {
					logger.Error(err, "Failed to authorize security group rule",
						"sgId", sgId, "sourceIp", rule.SourceIp, "protocol", rule.Protocol, "portRange", rule.PortRange)
				} else {
					logger.Info("Rule already exists, ignoring",
						"sgId", sgId, "sourceIp", rule.SourceIp, "protocol", rule.Protocol)
				}
				// 继续处理其他规则，不中断流程
			} else {
				addedCount++
				logger.Info("Successfully added ingress rule",
					"sgId", sgId, "sourceIp", rule.SourceIp, "protocol", rule.Protocol, "portRange", rule.PortRange)
			}
		}
	}

	logger.Info("Completed adding ingress rules", "sgId", sgId, "addedCount", addedCount, "totalNewIngressRules", newIngressRuleCount)
	logger.Info("Security group rules incremental update completed successfully", "sgId", sgId)
	return nil
}

// isRulesEqual 比较当前规则和新规则是否相等
func (handler *Public) isRulesEqual(currentRules []bcc.SecurityGroupRuleDetail, newRules []bcc.SecurityGroupRuleModel) bool {
	// 分别提取入站和出站规则
	currentIngress := make(map[string]bool)
	currentEgress := make(map[string]bool)
	newIngress := make(map[string]bool)
	newEgress := make(map[string]bool)

	// 构建当前规则的映射
	for _, rule := range currentRules {
		key := fmt.Sprintf("%s|%s|%s|%s|%s", rule.Direction, rule.Protocol, rule.PortRange, rule.SourceIp, rule.DestIp)
		if rule.Direction == "ingress" {
			currentIngress[key] = true
		} else if rule.Direction == "egress" {
			currentEgress[key] = true
		}
	}

	// 构建新规则的映射
	for _, rule := range newRules {
		key := fmt.Sprintf("%s|%s|%s|%s|%s", rule.Direction, rule.Protocol, rule.PortRange, rule.SourceIp, rule.DestIp)
		if rule.Direction == "ingress" {
			newIngress[key] = true
		} else if rule.Direction == "egress" {
			newEgress[key] = true
		}
	}

	// 比较入站规则
	if len(currentIngress) != len(newIngress) {
		return false
	}
	for key := range currentIngress {
		if !newIngress[key] {
			return false
		}
	}

	// 比较出站规则
	if len(currentEgress) != len(newEgress) {
		return false
	}
	for key := range currentEgress {
		if !newEgress[key] {
			return false
		}
	}

	return true
}

// bindSecurityGroupToEndpoint 绑定安全组到服务网卡
func (handler *Public) bindSecurityGroupToEndpoint(ctx context.Context, bccClient *bcc.Client, sgId, endpointId string, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	// 检查安全组和服务网卡是否在同一个VPC
	expectedVpcId := object.Spec.PrivateLinks[0].VPCID
	logger.Info("Binding security group to endpoint", "instance", object.GetName(), "sgId", sgId, "endpointId", endpointId, "vpcId", expectedVpcId)

	bindArgs := &bcc.UpdateEndpointNSGArgs{
		SecurityGroupIds: []string{sgId},
		ClientToken:      string(uuid.NewUUID()),
	}

	err := bccClient.UpdateEndpointNormalSecurityGroup(endpointId, bindArgs)
	if err != nil {
		logger.Error(err, "Failed to bind security group to endpoint", "sgId", sgId, "endpointId", endpointId, "vpcId", expectedVpcId)
		return fmt.Errorf("bind security group to endpoint failed: %w", err)
	}

	logger.Info("Security group bound to endpoint", "instance", object.GetName(), "sgId", sgId, "endpointId", endpointId)
	return nil
}

// cleanupSecurityGroup 清理安全组资源
func (handler *Public) cleanupSecurityGroup(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	if object.Status.PublicStatus.SecurityGroupId == "" {
		return nil
	}

	logger := log.FromContext(ctx)
	accountId := object.Spec.AccountID

	bccClient, err := handler.clients.BccClientFromAccount(accountId, "", handler.config.BCCEndpoint)
	if err != nil {
		logger.Error(err, "创建BCC客户端失败", "实例名称", object.GetName())
		return nil // 不阻塞删除流程
	}

	err = bccClient.DeleteSecurityGroup(object.Status.PublicStatus.SecurityGroupId, string(uuid.NewUUID()))
	if err != nil {
		logger.Error(err, "Failed to delete security group", "instance", object.GetName(), "securityGroupId", object.Status.PublicStatus.SecurityGroupId)
		return nil // don't block deletion process
	}

	logger.Info("Security group deleted successfully", "instance", object.GetName(), "securityGroupId", object.Status.PublicStatus.SecurityGroupId)
	object.Status.PublicStatus.SecurityGroupId = ""
	return nil
}

// handleCreateEIPAndBinding 函数用于创建并绑定EIP
//
// 参数 ctx：上下文对象
// 参数 object：CNCNetwork 对象指针
// 返回值：错误信息（如果存在）
func (handler *Public) handleCreateEIPAndBinding(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	// TODO list all eips
	if object.Status.PublicStatus.Address == "" {
		// 获取用户账户信息
		accountId := object.Spec.AccountID
		userId := object.Spec.UserID
		if userId == "" {
			// 如果UserID为空，使用privateLinks[].createdBy作为fallback
			for _, link := range object.Spec.PrivateLinks {
				if link.CreatedBy != "" {
					userId = link.CreatedBy
					break
				}
			}
			if userId == "" {
				userId = accountId // 最终fallback到accountId
			}
		}

		// 创建用户账户的EIP客户端
		logger.Info("开始创建用户账户EIP客户端", "实例名称", object.GetName(), "用户账户ID", accountId, "用户ID", userId, "EIP端点", handler.config.EIPEndpoint)
		userEipClient, err := handler.clients.EipClientFromAccount(accountId, userId, handler.config.EIPEndpoint)
		if err != nil {
			logger.Error(err, "创建用户账户EIP客户端失败", "实例名称", object.GetName(), "用户账户ID", accountId, "用户ID", userId)
			object.Status.PublicStatus.Reason = "createUserEipClientFailed"
			return fmt.Errorf("create user eip client failed: %w", err)
		}
		logger.Info("成功创建用户账户EIP客户端", "实例名称", object.GetName(), "用户账户ID", accountId, "用户ID", userId)

		// 检查是否已有EIP（查询所有状态的EIP以避免重复创建）
		logger.Info("开始查询用户账户下的所有EIP", "实例名称", object.GetName(), "用户账户ID", accountId)
		listResult, err := userEipClient.ListEip(&eip.ListEipArgs{}) // 不指定状态，查询所有EIP
		if err != nil {
			logger.Error(err, "查询用户账户下的EIP失败", "实例名称", object.GetName(), "用户账户ID", accountId)
			object.Status.PublicStatus.Reason = "listEipFailed"
			return fmt.Errorf("list eip failed: %w", err)
		}
		logger.Info("成功查询用户账户下的EIP", "实例名称", object.GetName(), "EIP总数量", len(listResult.EipList))

		needCreate := true
		var existingEip *eip.EipModel
		if len(listResult.EipList) != 0 {
			for _, v := range listResult.EipList {
				if v.Name == object.GetName() {
					logger.Info("发现已存在的同名EIP", "实例名称", object.GetName(), "EIP地址", v.Eip, "EIP状态", v.Status, "绑定类型", v.InstanceType, "绑定实例ID", v.InstanceId)
					existingEip = &v
					object.Status.PublicStatus.Address = v.Eip
					needCreate = false
					break
				}
			}
		}

		if existingEip != nil {
			logger.Info("将复用已存在的同名EIP", "实例名称", object.GetName(), "EIP地址", existingEip.Eip, "EIP状态", existingEip.Status)

			// 检查是否需要更新EIP配置
			needUpdateEip := false
			currentBandwidth := existingEip.BandWidthInMbps
			expectedBandwidth := object.Spec.PublicLink.BandwidthMbps

			// 如果配置了新的带宽且与当前不同，需要更新
			if expectedBandwidth > 0 && currentBandwidth != expectedBandwidth {
				needUpdateEip = true
				logger.Info("检测到EIP带宽配置变化，需要更新", "实例名称", object.GetName(),
					"EIP地址", existingEip.Eip, "当前带宽", currentBandwidth, "期望带宽", expectedBandwidth)
			}

			if needUpdateEip {
				// 调用EIP更新API
				err := handler.updateEipBandwidth(ctx, userEipClient, existingEip.Eip, expectedBandwidth)
				if err != nil {
					logger.Error(err, "更新EIP带宽失败", "实例名称", object.GetName(), "EIP地址", existingEip.Eip)
					object.Status.PublicStatus.Reason = "eipUpdateFailed"
					return fmt.Errorf("update eip bandwidth failed: %w", err)
				}
				logger.Info("EIP带宽更新成功", "实例名称", object.GetName(), "EIP地址", existingEip.Eip, "新带宽", expectedBandwidth)
			}
		}

		if needCreate {
			logger.Info("未发现同名EIP，需要创建新的EIP", "实例名称", object.GetName())
		}

		if needCreate {
			// 从CNCNetwork spec中读取配置，如果没有则使用默认值
			billingMethod := handler.eipBillingMethod // 默认值
			bandwidthMbps := 100                      // 默认值

			if object.Spec.PublicLink.BillingMethod != "" {
				billingMethod = object.Spec.PublicLink.BillingMethod
			}
			if object.Spec.PublicLink.BandwidthMbps > 0 {
				bandwidthMbps = object.Spec.PublicLink.BandwidthMbps
			}

			logger.Info("开始在用户账户下创建EIP", "实例名称", object.GetName(), "计费方式", billingMethod, "带宽", bandwidthMbps, "用户账户ID", accountId)

			// 使用带重试机制的EIP创建
			eipAddress, err := handler.createEipWithRetry(ctx, userEipClient, object.GetName(), billingMethod, bandwidthMbps)
			if err != nil {
				logger.Error(err, "在用户账户下创建EIP失败（已重试）", "实例名称", object.GetName(), "用户账户ID", accountId, "计费方式", billingMethod, "带宽", bandwidthMbps)
				object.Status.PublicStatus.Reason = "eipCreateFailed"
				return fmt.Errorf("create eip failed: %w", err)
			}

			object.Status.PublicStatus.Address = eipAddress
			logger.Info("成功在用户账户下创建EIP", "实例名称", object.GetName(), "EIP地址", eipAddress, "用户账户ID", accountId, "计费方式", billingMethod, "带宽", bandwidthMbps)
			logger.Info("created EIP in user account", "eipAddress", eipAddress, "accountId", accountId, "userId", userId)

			// find service network endpoint and bind EIP
			endpointID, err := handler.findServiceNetworkEndpoint(ctx, object)
			if err != nil {
				logger.Error(err, "Failed to find service network endpoint", "instance", object.GetName(), "eipAddress", eipAddress)
				// Note: keep EIP in user account for debugging when endpoint lookup fails
				logger.Info("EIP preserved in user account for debugging", "instance", object.GetName(), "eipAddress", eipAddress, "accountId", accountId)

				object.Status.PublicStatus.Reason = "findServiceNetworkFailed"
				return fmt.Errorf("find service network endpoint failed: %w", err)
			}
			logger.Info("成功找到服务网卡", "实例名称", object.GetName(), "EIP地址", eipAddress, "服务网卡ID", endpointID)

			// 绑定EIP到服务网卡（带重试机制）
			logger.Info("开始将EIP绑定到服务网卡", "实例名称", object.GetName(), "EIP地址", eipAddress, "服务网卡ID", endpointID)
			logger.Info("使用用户账户客户端进行EIP绑定操作", "实例名称", object.GetName(), "EIP地址", eipAddress, "用户账户ID", accountId)

			// bind EIP using user account EIP client
			err = handler.bindEipWithRetry(ctx, userEipClient, eipAddress, endpointID)
			if err != nil {
				logger.Error(err, "Failed to bind EIP to service network endpoint (with retry)", "instance", object.GetName(), "eipAddress", eipAddress, "endpointId", endpointID)
				// Note: keep EIP in user account for debugging when binding fails
				logger.Info("EIP preserved in user account for debugging", "instance", object.GetName(), "eipAddress", eipAddress, "accountId", accountId)

				handler.updatePublicStatusAtomically(object, eipAddress, ccrv1alpha1.EIPStatusClosed, "bindEipFailed")
				return fmt.Errorf("bind eip to service network failed: %w", err)
			}

			logger.Info("EIP bound to service network endpoint successfully, public access enabled", "instance", object.GetName(), "eipAddress", eipAddress, "endpointId", endpointID)
		}

		object.Status.PublicStatus.Reason = ""
	}

	// EIP creation and binding completed, atomically update status to opened
	handler.updatePublicStatusAtomically(object, object.Status.PublicStatus.Address, ccrv1alpha1.EIPStatusOpened, "")
	return nil
}

func (handler *Public) handleDeleteAndUnbindEip(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)
	logger.Info("Starting EIP deletion and unbinding", "instance", object.GetName())

	if object.Status.PublicStatus.Address == "" {
		handler.updatePublicStatusAtomically(object, "", ccrv1alpha1.EIPStatusClosed, "")
		return nil
	}

	eipAddress := object.Status.PublicStatus.Address
	logger.Info("开始删除EIP", "实例名称", object.GetName(), "EIP地址", eipAddress)

	// 域名功能已移除，无需处理DNS记录

	// 获取用户账户信息
	accountId := object.Spec.AccountID
	userId := object.Spec.UserID
	if userId == "" {
		// 如果UserID为空，使用privateLinks[].createdBy作为fallback
		for _, link := range object.Spec.PrivateLinks {
			if link.CreatedBy != "" {
				userId = link.CreatedBy
				break
			}
		}
		if userId == "" {
			userId = accountId // 最终fallback到accountId
		}
	}
	logger.Info("获取用户账户信息", "实例名称", object.GetName(), "用户账户ID", accountId, "用户ID", userId)

	// 创建用户账户的EIP客户端
	logger.Info("开始创建用户账户EIP客户端用于删除操作", "实例名称", object.GetName(), "用户账户ID", accountId)
	userEipClient, err := handler.clients.EipClientFromAccount(accountId, userId, handler.config.EIPEndpoint)
	if err != nil {
		logger.Error(err, "创建用户账户EIP客户端失败", "实例名称", object.GetName(), "用户账户ID", accountId, "用户ID", userId)
		object.Status.PublicStatus.Reason = "createUserEipClientFailed"
		return fmt.Errorf("create user eip client failed: %w", err)
	}
	logger.Info("成功创建用户账户EIP客户端", "实例名称", object.GetName(), "用户账户ID", accountId)

	logger.Info("开始查询EIP详细信息", "实例名称", object.GetName(), "EIP地址", eipAddress)
	listResult, err := userEipClient.ListEip(&eip.ListEipArgs{Eip: object.Status.PublicStatus.Address})
	if err != nil {
		logger.Error(err, "查询EIP详细信息失败", "实例名称", object.GetName(), "EIP地址", eipAddress)
		object.Status.PublicStatus.Reason = "eipListFailed"
		return err
	}

	if len(listResult.EipList) == 0 || listResult.EipList[0].Name != object.GetName() {
		logger.Info("EIP不存在或名称不匹配，视为已删除", "实例名称", object.GetName(), "EIP地址", eipAddress)
		handler.updatePublicStatusAtomically(object, "", ccrv1alpha1.EIPStatusClosed, "")
		return nil
	}

	eipInfo := listResult.EipList[0]
	logger.Info("查询到EIP详细信息", "实例名称", object.GetName(), "EIP地址", eipAddress, "EIP状态", eipInfo.Status, "绑定类型", eipInfo.InstanceType)

	switch eipInfo.Status {
	case "binded":
		logger.Info("EIP处于绑定状态，开始解绑", "实例名称", object.GetName(), "EIP地址", eipAddress, "绑定实例ID", eipInfo.InstanceId)
		logger.Info("使用用户账户客户端进行EIP解绑操作", "实例名称", object.GetName(), "EIP地址", eipAddress, "用户账户ID", accountId)

		// 使用用户账户的EIP客户端进行解绑操作
		err = userEipClient.UnBindEip(object.Status.PublicStatus.Address, string(uuid.NewUUID()))
		if err != nil {
			logger.Error(err, "EIP解绑失败", "实例名称", object.GetName(), "EIP地址", eipAddress)
			object.Status.PublicStatus.Reason = "unbindingFailed"
			return err
		}
		logger.Info("EIP解绑成功", "实例名称", object.GetName(), "EIP地址", eipAddress)

		logger.Info("EIP已解绑，等待状态变为可用", "实例名称", object.GetName(), "EIP地址", eipAddress)
		return nil
	case "unbinding":
		logger.Info("EIP正在解绑中，等待解绑完成", "实例名称", object.GetName(), "EIP地址", eipAddress)
		object.Status.PublicStatus.Reason = "unbinding"
		return nil
	case "available":
		logger.Info("EIP处于可用状态，开始删除", "实例名称", object.GetName(), "EIP地址", eipAddress)
		err = userEipClient.DeleteEip(object.Status.PublicStatus.Address, string(uuid.NewUUID()))
		if err != nil {
			logger.Error(err, "删除EIP失败", "实例名称", object.GetName(), "EIP地址", eipAddress)
			object.Status.PublicStatus.Reason = "deleteEipFailed"
			return err
		}

		handler.updatePublicStatusAtomically(object, "", ccrv1alpha1.EIPStatusClosed, "")
		logger.Info("成功从用户账户删除EIP，公网访问已关闭", "实例名称", object.GetName(), "EIP地址", eipAddress, "用户账户ID", accountId, "用户ID", userId)
		return nil
	default:
		logger.Info("EIP状态未知，跳过处理", "实例名称", object.GetName(), "EIP地址", eipAddress, "EIP状态", eipInfo.Status)
	}

	return nil
}

func (handler *Public) createDNSRecordParallel(ctx context.Context, domain utils.Domain, ip string, resolved bool) error {
	var wg sync.WaitGroup

	wg.Add(2)

	var errRdns, errDns error

	go func() {
		errDns = handler.createDNSRecordIfNotExists(ctx, domain, ip, "inet", resolved)
		wg.Done()
	}()

	go func() {
		errRdns = handler.createDNSRecordIfNotExists(ctx, domain, ip, "pnet", resolved)
		wg.Done()
	}()

	wg.Wait()

	if errRdns != nil || errDns != nil {
		return fmt.Errorf("create DNSRecord failed: rdns=%s, dns=%s", errRdns, errDns)
	}

	return nil
}

func (handler *Public) createDNSRecordIfNotExists(ctx context.Context, domain utils.Domain, ip, view string, resolved bool) error {
	logger := log.FromContext(ctx)
	// try created
	var err error

	if !resolved {
		record := &dns.DNSRecord{
			Domain: domain.GetName(),
			Zone:   domain.GetZone(),
			Type:   "A",
			TTL:    600,
			Rdata:  ip,
			View:   domain.GetView(view),
		}

		switch view {
		case "inet":
			err = handler.clients.DnsClient().AddRecord(record)
		case "pnet":
			err = handler.clients.DnsClient().AddRDNSRecord(record)
		default:
			return fmt.Errorf("invalid view for dns record: %s", view)
		}

		if err != nil {
			var dnsErr *dns.DNSError
			if errors.As(err, &dnsErr) && dnsErr.IsAlreadyExist() {
				return nil
			}

			logger.V(2).Error(err, fmt.Sprintf("create dns record failed"))
			return err
		}

		return nil
	}

	var ips []string
	switch view {
	case "inet":
		ips, err = DNSLookup(domain.String(), fmt.Sprintf("%s:53", handler.config.InetDNSServer))
	case "pnet":
		ips, err = DNSLookup(domain.String(), fmt.Sprintf("%s:53", handler.config.PnetDNSServer))
	default:
		return fmt.Errorf("invalid view for dns record: %s", view)
	}

	if err != nil {
		logger.V(9).Error(err, "lookup host for view: "+view)
		var dnsErr *net.DNSError
		if errors.As(err, &dnsErr) && dnsErr.IsNotFound {
			// no dns found
			return fmt.Errorf("dns host in %s is not found", view)
		}

		return err
	}

	if len(ips) == 0 {
		return fmt.Errorf("dns record is empty")
	}

	for _, v := range ips {
		if v == ip {
			return nil
		}
	}

	return fmt.Errorf("dns record is not found")
}

func (handler *Public) removeDNSRecordIfExists(ctx context.Context, domain utils.Domain, ip, view string) error {
	logger := log.FromContext(ctx)
	// try created
	var err error

	record := &dns.DNSRecord{
		Domain: domain.GetName(),
		Zone:   domain.GetZone(),
		Type:   "A",
		TTL:    600,
		Rdata:  ip,
		View:   domain.GetView(view),
	}

	switch view {
	case "inet":
		err = handler.clients.DnsClient().DeleteRecord(record)
	case "pnet":
		err = handler.clients.DnsClient().DeleteRDNSRecord(record)
	default:
		return fmt.Errorf("invalid view for dns record: %s", view)
	}

	if err != nil {
		var dnsErr *dns.DNSError
		if errors.As(err, &dnsErr) && dnsErr.IsNotFound() {
			return nil
		}

		logger.V(2).Error(err, fmt.Sprintf("delete dns record failed"))
		return err
	}

	return nil
}

// findServiceNetworkEndpoint 查找对应的服务网卡ID
func (handler *Public) findServiceNetworkEndpoint(ctx context.Context, object *ccrv1alpha1.CNCNetwork) (string, error) {
	logger := log.FromContext(ctx)

	// 遍历所有私有链接，查找对应的服务网卡
	for _, privateLink := range object.Spec.PrivateLinks {
		accountId := object.Spec.AccountID
		userId := privateLink.CreatedBy
		if userId == "" {
			userId = accountId // fallback to accountId
		}

		// 创建用户账户的VPC客户端
		vpcCli, err := handler.clients.VpcClientFromAccount(accountId, userId, handler.config.VPCEndpoint)
		if err != nil {
			logger.Error(err, "create vpc client failed", "accountId", accountId, "userId", userId)
			continue
		}

		// 查找服务网卡
		logger.Info("开始在指定VPC和子网中查找服务网卡", "实例名称", object.GetName(), "VPC ID", privateLink.VPCID, "子网ID", privateLink.SubnetID, "用户账户ID", accountId)
		endpoints, err := vpcCli.ListServiceNetwork(privateLink.VPCID, handler.resourceSourceName, privateLink.SubnetID, object.GetName(), "", handler.config.VpcServiceNetworkHexKey, accountId, 100)
		if err != nil {
			logger.Error(err, "查询服务网卡列表失败", "实例名称", object.GetName(), "VPC ID", privateLink.VPCID, "子网ID", privateLink.SubnetID)
			continue
		}
		logger.Info("成功查询服务网卡列表", "实例名称", object.GetName(), "VPC ID", privateLink.VPCID, "服务网卡数量", len(endpoints.Result))

		// 查找匹配的服务网卡
		for _, endpoint := range endpoints.Result {
			logger.Info("检查服务网卡", "实例名称", object.GetName(), "服务网卡名称", endpoint.Name, "服务网卡ID", endpoint.EndpointID, "状态", endpoint.Status)
			if endpoint.Name == object.GetName() {
				logger.Info("找到匹配的服务网卡", "实例名称", object.GetName(), "服务网卡ID", endpoint.EndpointID, "服务网卡名称", endpoint.Name, "IP地址", endpoint.IPAddress)
				return endpoint.EndpointID, nil
			}
		}
	}

	return "", fmt.Errorf("no matching service network endpoint found for %s", object.GetName())
}

// updateEipBandwidth 更新EIP带宽配置
func (handler *Public) updateEipBandwidth(ctx context.Context, userEipClient *eip.Client, eipAddress string, bandwidthMbps int) error {
	logger := log.FromContext(ctx)

	logger.Info("开始更新EIP带宽", "EIP地址", eipAddress, "新带宽", bandwidthMbps)

	updateArgs := &eip.ResizeEipArgs{
		NewBandWidthInMbps: bandwidthMbps,
		ClientToken:        string(uuid.NewUUID()),
	}

	err := userEipClient.ResizeEip(eipAddress, updateArgs)
	if err != nil {
		logger.Error(err, "更新EIP带宽失败", "EIP地址", eipAddress, "新带宽", bandwidthMbps)
		return fmt.Errorf("resize eip bandwidth failed: %w", err)
	}

	logger.Info("EIP带宽更新成功", "EIP地址", eipAddress, "新带宽", bandwidthMbps)
	return nil
}

// bindEipWithRetry 带重试机制的EIP绑定方法
func (handler *Public) bindEipWithRetry(ctx context.Context, userEipClient *eip.Client, eipAddress, endpointID string) error {
	logger := log.FromContext(ctx)

	for i := 0; i < 3; i++ {
		logger.Info("开始EIP绑定尝试", "EIP地址", eipAddress, "服务网卡ID", endpointID, "尝试次数", i+1, "总次数", 3)

		bindArgs := &eip.BindEipArgs{
			InstanceType: "SNIC", // 服务网卡类型
			InstanceId:   endpointID,
			ClientToken:  string(uuid.NewUUID()),
		}

		err := userEipClient.BindEip(eipAddress, bindArgs)
		if err == nil {
			logger.Info("EIP绑定成功", "EIP地址", eipAddress, "服务网卡ID", endpointID, "尝试次数", i+1)
			return nil
		}

		logger.Error(err, "EIP绑定失败，准备重试", "EIP地址", eipAddress, "服务网卡ID", endpointID, "尝试次数", i+1)

		// 最后一次重试失败，直接返回错误
		if i == 2 {
			logger.Error(err, "EIP绑定最终失败，已达到最大重试次数", "EIP地址", eipAddress, "服务网卡ID", endpointID, "总尝试次数", i+1)
			return fmt.Errorf("bind eip failed after %d retries: %w", i+1, err)
		}

		// exponential backoff retry interval
		sleepDuration := time.Duration(i+1) * time.Second
		logger.Info("Waiting before retry", "eipAddress", eipAddress, "waitTime", sleepDuration, "nextAttempt", i+2)
		time.Sleep(sleepDuration)
	}

	return fmt.Errorf("bind eip failed after retries")
}

// updatePublicStatusAtomically atomically updates public access status
func (handler *Public) updatePublicStatusAtomically(object *ccrv1alpha1.CNCNetwork, address, status, reason string) {
	object.Status.PublicStatus.Address = address
	object.Status.PublicStatus.Status = status
	object.Status.PublicStatus.Reason = reason
}

// createEipWithRetry creates EIP with retry mechanism and conflict detection
func (handler *Public) createEipWithRetry(ctx context.Context, userEipClient *eip.Client, instanceName, billingMethod string, bandwidthMbps int) (string, error) {
	logger := log.FromContext(ctx)

	for i := 0; i < 3; i++ {
		logger.Info("开始EIP创建尝试", "实例名称", instanceName, "尝试次数", i+1, "总次数", 3)

		// 在每次重试前，先检查是否已经有同名的EIP被创建
		logger.Info("检查是否已有同名EIP被创建", "实例名称", instanceName)
		listResult, err := userEipClient.ListEip(&eip.ListEipArgs{})
		if err == nil && len(listResult.EipList) > 0 {
			for _, v := range listResult.EipList {
				if v.Name == instanceName {
					logger.Info("发现已创建的同名EIP，将复用", "实例名称", instanceName, "EIP地址", v.Eip, "EIP状态", v.Status, "尝试次数", i+1)
					return v.Eip, nil
				}
			}
		}

		// 尝试创建EIP
		logger.Info("开始创建EIP", "实例名称", instanceName, "计费方式", billingMethod, "带宽", bandwidthMbps)
		eipResult, err := userEipClient.CreateEip(&eip.CreateEipArgs{
			Name:            instanceName,
			BandWidthInMbps: bandwidthMbps,
			Billing: &eip.Billing{
				PaymentTiming: "Postpaid",
				BillingMethod: billingMethod,
			},
			ClientToken: string(uuid.NewUUID()),
		})

		if err == nil {
			logger.Info("EIP创建成功", "实例名称", instanceName, "EIP地址", eipResult.Eip, "尝试次数", i+1)
			return eipResult.Eip, nil
		}

		// check if it's a conflict error (name already exists)
		var bceErr *bce.BceServiceError
		if errors.As(err, &bceErr) && (bceErr.StatusCode == http.StatusConflict ||
			bceErr.StatusCode == http.StatusBadRequest &&
				(bceErr.Code == "DuplicateName" || bceErr.Code == "ResourceAlreadyExists")) {
			logger.Info("Detected EIP name conflict, will check existing resources", "instance", instanceName, "attempt", i+1, "errorCode", bceErr.Code)
			// conflict error, continue to next retry (will check existing resources first)
			continue
		}

		logger.Error(err, "EIP creation failed, preparing retry", "instance", instanceName, "attempt", i+1)

		// last retry failed, return error directly
		if i == 2 {
			logger.Error(err, "EIP creation finally failed, reached maximum retry count", "instance", instanceName, "totalAttempts", i+1)
			return "", fmt.Errorf("create eip failed after %d retries: %w", i+1, err)
		}

		// 指数退避重试间隔
		sleepDuration := time.Duration(i+1) * time.Second
		logger.Info("等待后重试", "实例名称", instanceName, "等待时间", sleepDuration, "下次尝试", i+2)
		time.Sleep(sleepDuration)
	}

	return "", fmt.Errorf("create eip failed after retries")
}

// getCurrentEIPBandwidth 获取当前EIP的带宽配置
func (handler *Public) getCurrentEIPBandwidth(ctx context.Context, object *ccrv1alpha1.CNCNetwork) (int, error) {
	logger := log.FromContext(ctx)

	eipAddress := object.Status.PublicStatus.Address
	if eipAddress == "" {
		return 0, fmt.Errorf("no EIP address found")
	}

	logger.Info("Getting current EIP bandwidth",
		"instance", object.GetName(), "eipAddress", eipAddress)

	// 创建EIP客户端
	accountId := object.Spec.AccountID
	userEipClient, err := handler.clients.EipClientFromAccount(accountId, "", handler.config.EIPEndpoint)
	if err != nil {
		logger.Error(err, "Failed to create EIP client", "instance", object.GetName())
		return 0, fmt.Errorf("create eip client failed: %w", err)
	}

	// 通过ListEip获取EIP详情
	listArgs := &eip.ListEipArgs{
		Eip: eipAddress,
	}

	eipList, err := userEipClient.ListEip(listArgs)
	if err != nil {
		logger.Error(err, "Failed to list EIP details",
			"instance", object.GetName(), "eipAddress", eipAddress)
		return 0, fmt.Errorf("list eip details failed: %w", err)
	}

	if eipList == nil || len(eipList.EipList) == 0 {
		logger.Error(nil, "EIP not found in list",
			"instance", object.GetName(), "eipAddress", eipAddress)
		return 0, fmt.Errorf("eip not found: %s", eipAddress)
	}

	// 找到匹配的EIP
	var targetEip *eip.EipModel
	for _, eipItem := range eipList.EipList {
		if eipItem.Eip == eipAddress {
			targetEip = &eipItem
			break
		}
	}

	if targetEip == nil {
		logger.Error(nil, "EIP not found in list results",
			"instance", object.GetName(), "eipAddress", eipAddress)
		return 0, fmt.Errorf("eip not found in results: %s", eipAddress)
	}

	currentBandwidth := targetEip.BandWidthInMbps
	logger.Info("Retrieved current EIP bandwidth",
		"instance", object.GetName(), "eipAddress", eipAddress,
		"currentBandwidth", currentBandwidth)

	return currentBandwidth, nil
}

// updateEIPConfiguration 更新EIP配置
func (handler *Public) updateEIPConfiguration(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	eipAddress := object.Status.PublicStatus.Address
	specBandwidth := object.Spec.PublicLink.BandwidthMbps

	logger.Info("Starting EIP configuration update",
		"instance", object.GetName(), "eipAddress", eipAddress,
		"newBandwidth", specBandwidth)

	// 创建EIP客户端
	accountId := object.Spec.AccountID
	userEipClient, err := handler.clients.EipClientFromAccount(accountId, "", handler.config.EIPEndpoint)
	if err != nil {
		logger.Error(err, "Failed to create EIP client", "instance", object.GetName())
		return fmt.Errorf("create eip client failed: %w", err)
	}

	// 调用EIP带宽更新方法（复用之前实现的方法）
	err = handler.updateEipBandwidth(ctx, userEipClient, eipAddress, specBandwidth)
	if err != nil {
		logger.Error(err, "Failed to update EIP bandwidth",
			"instance", object.GetName(), "eipAddress", eipAddress, "newBandwidth", specBandwidth)
		object.Status.PublicStatus.Reason = "eipBandwidthUpdateFailed"
		return fmt.Errorf("update eip bandwidth failed: %w", err)
	}

	logger.Info("EIP configuration update completed successfully",
		"instance", object.GetName(), "eipAddress", eipAddress, "newBandwidth", specBandwidth)

	// 清除可能的错误状态
	if object.Status.PublicStatus.Reason == "eipBandwidthUpdateFailed" {
		object.Status.PublicStatus.Reason = ""
	}

	return nil
}
