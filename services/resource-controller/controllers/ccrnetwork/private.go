package ccrnetwork

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	vpcsdk "github.com/baidubce/bce-sdk-go/services/vpc"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Private struct {
	clients clientset.ClientSetInterface

	config             *conf.Config
	k8sclient          client.Client
	resourceSourceName string
}

func NewPrivate(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Private {
	return &Private{
		config:             config,
		clients:            clients,
		k8sclient:          cli,
		resourceSourceName: config.ServiceName,
	}
}

// handlePrivate 比对spec和status的privatelink, 处理新增、删除、修改三种变更
func (handler *Private) handlePrivate(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)
	// 通过vpc+subnet唯一标识一个服务网卡
	buildKey := func(i interface{}) string {
		switch v := i.(type) {
		case ccrv1alpha1.PrivateLink:
			return v.VPCID + v.SubnetID
		case ccrv1alpha1.PrivateLinkStatus:
			return v.VPCID + v.SubnetID
		}
		return ""
	}

	desired, current := map[string]string{}, map[string]string{}
	for _, v := range object.Spec.PrivateLinks {
		desired[buildKey(v)] = v.EsgID
	}

	linkStatus := make([]ccrv1alpha1.PrivateLinkStatus, 0)
	for _, v := range object.Status.LinkStatus {
		if v.Status == ccrv1alpha1.PrivateLinkStatusDeleted {
			continue
		}

		// deleting has higher priority than creating
		_, ok := desired[buildKey(v)]
		if !ok && handler.isPrivateNetStable(&v) {
			v.Status = ccrv1alpha1.PrivateLinkStatusDeleting
		}

		linkStatus = append(linkStatus, v)
		current[buildKey(v)] = v.EsgID
	}

	for _, v := range object.Spec.PrivateLinks {
		currEsgId, ok := current[buildKey(v)]
		if !ok {
			st := ccrv1alpha1.PrivateLinkStatus{
				VPCID:     v.VPCID,
				SubnetID:  v.SubnetID,
				IP:        v.IPAddr,
				Status:    ccrv1alpha1.PrivateLinkStatusCreating,
				CreatedBy: v.CreatedBy,
			}

			if v.AutoDNS && object.Spec.Domain.PrivateDomain != "" {
				st.Domain = object.Spec.Domain.PrivateDomain
			}

			if v.EsgID != "" {
				st.EsgID = v.EsgID
			}

			linkStatus = append(linkStatus, st)
		} else if v.EsgID != currEsgId && v.EsgID != "" {
			// 需要更新安全组的场景
			for i := 0; i < len(linkStatus); i++ {
				if linkStatus[i].VPCID+linkStatus[i].SubnetID == v.VPCID+v.SubnetID {
					linkStatus[i].EsgID = v.EsgID
					linkStatus[i].Status = ccrv1alpha1.PrivateLinkStatusRebindingEsg
					break
				}
			}
		}
	}

	for idx, v := range linkStatus {
		userId := object.Spec.UserID
		if v.CreatedBy != "" {
			userId = v.CreatedBy
		}

		err := handler.handleOnePrivate(ctx, object.Spec.AccountID, userId, object.GetName(), object.Status.PublishPoint, &v)
		if err != nil {
			logger.Error(err, "handle one private")
		}
		linkStatus[idx] = v
	}

	object.Status.LinkStatus = linkStatus

	return nil
}

// handleOnePrivate 创建服务网卡/删除服务网卡/绑定安全组
func (handler *Private) handleOnePrivate(ctx context.Context, accountId, userId, serviceName, serviceURL string, st *ccrv1alpha1.PrivateLinkStatus) error {
	logger := log.FromContext(ctx)
	vpcCli, err := handler.clients.VpcClientFromAccount(accountId, userId, handler.config.VPCEndpoint)
	if err != nil {
		logger.Error(err, "create vpc client failed")
		return err
	}

	longIdMap, err := vpcCli.MapByShortIds([]string{st.VPCID})

	if err != nil {
		st.Status = ccrv1alpha1.PrivateLinkStatusFailed
		st.Reason = "getLongIDFailed"

		var bceErr *bce.BceServiceError
		// bad request means client error, record and continue
		if errors.As(err, &bceErr) && bceErr.StatusCode == http.StatusBadRequest {
			logger.Error(err, "vpcid is invalid")
			return nil
		}

		logger.Error(err, fmt.Sprintf("get long id %v failed", st.VPCID))
		return err
	}

	current := metav1.Now()

	if st.Status == ccrv1alpha1.PrivateLinkStatusCreating {
		_, err := vpcCli.GetVPCDetail(st.VPCID)
		if err != nil {
			logger.Error(err, fmt.Sprintf("get vpc %s details failed", st.VPCID))
			var bceErr *bce.BceServiceError
			if errors.As(err, &bceErr) && bceErr.StatusCode == http.StatusNotFound {
				st.Status = ccrv1alpha1.PrivateLinkStatusFailed
				st.Reason = "vpc is not found"
				// stop
				return nil
			}

			st.Reason = "getVpcDetailsFailed"
			return err
		}

		listResp, err := vpcCli.ListServiceNetwork(longIdMap[st.VPCID], handler.resourceSourceName, "",
			serviceName, "", handler.config.VpcServiceNetworkHexKey, handler.config.UserID, 1000)
		if err != nil {
			logger.Error(err, "Failed to list service networks", "serviceName", serviceName, "vpcId", st.VPCID)
			st.Reason = "listServiceNetFailed"
			return err
		}

		if listResp != nil && len(listResp.Result) != 0 {
			logger.Info("Found existing service network endpoint, reusing", "serviceName", serviceName, "endpointId", listResp.Result[0].EndpointID, "ipAddress", listResp.Result[0].IPAddress, "status", listResp.Result[0].Status)
			st.IP = listResp.Result[0].IPAddress
			st.ServiceID = listResp.Result[0].EndpointID
			st.Reason = ""
			if listResp.Result[0].Status == "available" {
				// create private dns if possible
				if st.Domain != "" {
					err = handler.createVpcDNSRecord(ctx, longIdMap[st.VPCID], accountId, userId, st)
					if err != nil {
						logger.Error(err, "Failed to create private DNS record", "serviceName", serviceName, "domain", st.Domain)
						st.Reason = "createDNSRecordFailed"
						st.Status = ccrv1alpha1.PrivateLinkStatusFailed
						return nil
					}
				}

				if st.EsgID != "" {
					err = vpcCli.UpdateEndpointESGs(st.ServiceID, &vpc.UpdateEndpointESGsArgs{
						ClientToken: string(uuid.NewUUID()),
						EsgIds:      []string{st.EsgID},
					})
					if err != nil {
						logger.Error(err, "Failed to bind security group", "serviceName", serviceName, "endpointId", st.ServiceID, "esgId", st.EsgID)
						st.Reason = "bindEsgFailed"
						st.Status = ccrv1alpha1.PrivateLinkStatusFailed
						return nil
					}
				}

				st.Status = ccrv1alpha1.PrivateLinkStatusCreated
				st.StartTime = &current
				logger.Info("Service network endpoint configuration completed", "serviceName", serviceName, "endpointId", st.ServiceID, "status", st.Status)
				return nil
			}

			return fmt.Errorf("service network not available, waiting for next round")
		}

		// create service network with retry mechanism
		resp, err := handler.createServiceNetworkWithRetry(ctx, vpcCli, serviceName, serviceURL, st)
		if err != nil {
			logger.Error(err, "Failed to create service network endpoint (with retry)", "serviceName", serviceName, "vpcId", st.VPCID, "subnetId", st.SubnetID)
			st.Reason = "createServiceNetworkFailed: " + err.Error()
			st.Status = ccrv1alpha1.PrivateLinkStatusFailed
			return err
		}
		logger.Info("Service network endpoint created successfully", "serviceName", serviceName, "endpointId", resp.ID, "ipAddress", resp.IPAddress)

		st.IP = resp.IPAddress
		st.ServiceID = resp.ID
		st.ResourceSource = handler.resourceSourceName
		st.StartTime = &current
		st.Reason = "Creating"
		st.CreatedBy = userId

		return fmt.Errorf("waiting for available, next round")
	}

	if st.Status == ccrv1alpha1.PrivateLinkStatusDeleting {
		if st.Domain != "" {
			//
			err = handler.deleteVpcDNSRecord(ctx, longIdMap[st.VPCID], accountId, userId, st)
			if err != nil {
				st.Reason = "deleteVpcDNSRecordFailed"
				return nil
			}

			st.Domain = ""
		}

		if st.ServiceID != "" {
			if st.ResourceSource == handler.resourceSourceName {
				err = vpcCli.DeleteServiceNetwork(st.ServiceID, handler.resourceSourceName,
					string(uuid.NewUUID()), handler.config.VpcServiceNetworkHexKey, handler.config.UserID)

			} else {
				err = vpcCli.DeleteServiceNetwork(st.ServiceID, handler.resourceSourceName,
					string(uuid.NewUUID()), "", "")
			}

			var bceErr *bce.BceServiceError
			if (err != nil && errors.As(err, &bceErr) && bceErr.StatusCode == http.StatusNotFound) ||
				err == nil {
				st.Status = ccrv1alpha1.PrivateLinkStatusDeleted
				st.Reason = ""
				return nil
			}

			if err != nil {
				st.Reason = "deleteServiceNetworkFailed"
				return err
			}
		}

		st.Status = ccrv1alpha1.PrivateLinkStatusDeleted
		st.Reason = ""
		return nil
	}

	if st.Status == ccrv1alpha1.PrivateLinkStatusRebindingEsg {
		if st.EsgID != "" {
			err = vpcCli.UpdateEndpointESGs(st.ServiceID, &vpc.UpdateEndpointESGsArgs{
				ClientToken: string(uuid.NewUUID()),
				EsgIds:      []string{st.EsgID},
			})
			if err != nil {
				logger.Error(err, "rebind esg failed")
				st.Reason = fmt.Sprintf("rebind esg failed, %v", err)
				st.Status = ccrv1alpha1.PrivateLinkStatusFailed
				return nil
			}
		}
		st.Status = ccrv1alpha1.PrivateLinkStatusCreated
	}
	return nil
}

func (handler *Private) createVpcDNSRecord(ctx context.Context, vpcLongId, accoutId, userId string, st *ccrv1alpha1.PrivateLinkStatus) error {
	logger := log.FromContext(ctx)
	requestID := string(uuid.NewUUID())

	if st.Domain == "" {
		return nil
	}

	logger.Info("Creating DNS record", "domain", st.Domain)

	vpcDnsClient, err := handler.clients.VpcDnsClientFromAccount(accoutId, userId, handler.config.VPCDNSEndpoint)
	if err != nil {
		logger.Error(err, "create vpc dns client failed")
		return err
	}

	vpcDNSRecordData := &dns.VpcDNSRecordData{}
	if st.RecordID != "" {
		resp, err := vpcDnsClient.GetVpcRecord(requestID, handler.resourceSourceName,
			handler.config.VpcDNSHexKey, handler.config.UserID, st.RecordID)
		if err != nil {
			logger.Error(err, "get dns record failed")
			return err
		}
		vpcDNSRecordData = resp.Record
	} else {
		resp, err := vpcDnsClient.ListVpcRecord(requestID, handler.resourceSourceName,
			handler.config.VpcDNSHexKey, handler.config.UserID, vpcLongId, st.Domain, "", "", "1000")
		if err != nil {
			logger.Error(err, "list dns record failed")
			return err
		}
		if len(resp.Records) > 0 {
			vpcDNSRecordData = resp.Records[0]
		}
	}

	if vpcDNSRecordData != nil && vpcDNSRecordData.Id != "" {
		if st.RecordID != vpcDNSRecordData.Id {
			st.RecordID = vpcDNSRecordData.Id
		}
		return nil
	}

	userInfo, err := handler.clients.IAMClient().GetUsers(&iam.UserListRequest{
		DomainID: accoutId,
		Name:     "root",
	})
	if err != nil {
		logger.Error(err, "list user info failed")
		return err
	}

	if len(userInfo.UserList) == 0 {
		logger.Error(fmt.Errorf("no user found"), "found user failed")
		return fmt.Errorf("no user found")
	}

	resp, err := vpcDnsClient.CreateVpcRecord(requestID, handler.resourceSourceName,
		handler.config.VpcDNSHexKey, handler.config.UserID, &dns.CreateVPCDNSRecordRequest{
			VpcId:  vpcLongId,
			Name:   st.Domain,
			Domain: "baidubce.com",
			Type:   "A",
			Value:  st.IP,
			Ttl:    60,
		})
	if err != nil {
		logger.Error(err, "Failed to create VPC DNS record")
	} else {
		logger.Info("VPC DNS record created successfully", "domain", st.Domain, "recordId", resp.Id)
	}
	st.RecordID = resp.Id

	return err
}

func (handler *Private) deleteVpcDNSRecord(ctx context.Context, vpcLongId, accoutId, userId string, st *ccrv1alpha1.PrivateLinkStatus) error {
	logger := log.FromContext(ctx)
	requestID := string(uuid.NewUUID())

	if st.Domain == "" {
		return nil
	}

	logger.Info("Deleting DNS record", "domain", st.Domain)

	vpcDnsClient, err := handler.clients.VpcDnsClientFromAccount(accoutId, userId, handler.config.VPCDNSEndpoint)
	if err != nil {
		logger.Error(err, "create vpc dns client failed")
		return err
	}


	vpcDNSRecordData := &dns.VpcDNSRecordData{}
	if st.RecordID != "" {
		resp, err := vpcDnsClient.GetVpcRecord(requestID, handler.resourceSourceName,
			handler.config.VpcDNSHexKey, handler.config.UserID, st.RecordID)
		if err != nil {
			logger.Error(err, "get dns record failed")
			return err
		}
		vpcDNSRecordData = resp.Record
	} else {
		resp, err := vpcDnsClient.ListVpcRecord(requestID, handler.resourceSourceName,
			handler.config.VpcDNSHexKey, handler.config.UserID, vpcLongId, st.Domain, "", "", "1000")
		if err != nil {
			logger.Error(err, "list dns record failed")
			return err
		}
		if len(resp.Records) > 0 {
			vpcDNSRecordData = resp.Records[0]
		}
	}

	if vpcDNSRecordData == nil || vpcDNSRecordData.Id == "" {
		st.RecordID = ""
		return nil
	}

	err = vpcDnsClient.DeleteVpcRecord(requestID, handler.resourceSourceName,
		handler.config.VpcDNSHexKey, handler.config.UserID, vpcDNSRecordData.Id)

	if err != nil {
		logger.Error(err, "Failed to delete VPC DNS record")
	} else {
		logger.Info("VPC DNS record deleted successfully", "domain", st.Domain)
	}
	st.RecordID = ""

	return err
}

// isPrivateNetStable checks if private link has reached terminal state
func (handler *Private) isPrivateNetStable(st *ccrv1alpha1.PrivateLinkStatus) bool {
	return st.Status == ccrv1alpha1.PrivateLinkStatusCreated ||
		st.Status == ccrv1alpha1.PrivateLinkStatusDeleted ||
		st.Status == ccrv1alpha1.PrivateLinkStatusFailed
}

// createServiceNetworkWithRetry creates service network endpoint with retry mechanism
func (handler *Private) createServiceNetworkWithRetry(ctx context.Context, vpcCli *vpc.Client, serviceName, serviceURL string, st *ccrv1alpha1.PrivateLinkStatus) (*vpc.CreateServiceNetworkResponse, error) {
	logger := log.FromContext(ctx)

	for i := 0; i < 3; i++ {
		// check if service network endpoint with same name already exists before each retry
		listResp, err := vpcCli.ListServiceNetwork(st.VPCID, handler.resourceSourceName, "",
			serviceName, "", handler.config.VpcServiceNetworkHexKey, handler.config.UserID, 1000)
		if err == nil && listResp != nil && len(listResp.Result) != 0 {
			// found existing service network endpoint, return simulated response
			logger.Info("Found existing service network endpoint with same name, reusing", "serviceName", serviceName, "endpointId", listResp.Result[0].EndpointID, "ipAddress", listResp.Result[0].IPAddress, "attempt", i+1)
			return &vpc.CreateServiceNetworkResponse{
				ID:        listResp.Result[0].EndpointID,
				IPAddress: listResp.Result[0].IPAddress,
			}, nil
		}

		// attempt to create service network endpoint
		resp, err := vpcCli.CreateServiceNetwork(handler.config.VpcServiceNetworkHexKey, handler.resourceSourceName,
			handler.config.UserID, &vpc.CreateServiceNetworkArgs{
				ClientToken: string(uuid.NewUUID()),
				VpcID:       st.VPCID,
				Name:        serviceName,
				SubnetID:    st.SubnetID,
				Service:     serviceURL,
				IPAddress:   st.IP,
				Billing: &vpcsdk.Billing{
					PaymentTiming: "Postpaid",
				},
			})

		if err == nil {
			logger.Info("Service network endpoint created successfully", "serviceName", serviceName, "endpointId", resp.ID, "ipAddress", resp.IPAddress, "attempt", i+1)
			return resp, nil
		}

		// check if it's a conflict error (name already exists)
		var bceErr *bce.BceServiceError
		if errors.As(err, &bceErr) && (bceErr.StatusCode == http.StatusConflict ||
			bceErr.StatusCode == http.StatusBadRequest &&
			(bceErr.Code == "DuplicateName" || bceErr.Code == "ResourceAlreadyExists")) {
			logger.Info("Detected service network endpoint name conflict, will check existing resources", "serviceName", serviceName, "attempt", i+1, "errorCode", bceErr.Code)
			// conflict error, continue to next retry (will check existing resources first)
			continue
		}

		logger.Error(err, "Service network endpoint creation failed, preparing retry", "serviceName", serviceName, "attempt", i+1)

		// last retry failed, return error directly
		if i == 2 {
			logger.Error(err, "Service network endpoint creation finally failed, reached maximum retry count", "serviceName", serviceName, "totalAttempts", i+1)
			return nil, fmt.Errorf("create service network failed after %d retries: %w", i+1, err)
		}

		// exponential backoff retry interval
		sleepDuration := time.Duration(i+1) * time.Second
		time.Sleep(sleepDuration)
	}

	return nil, fmt.Errorf("create service network failed after retries")
}
