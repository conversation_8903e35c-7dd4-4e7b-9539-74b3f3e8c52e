# 灰度环境
accessKey: ALTAKrl2vcxH633BZpOeuvxHC6
secretKey: 41bb7511667f461598b7d49c644f5b8d

clusterId: cce-ma5djy82
userId: 21609f0c2f29409c8567840642f721d2
vpcDnsHexKey:
vpcServiceNetworkHexKey:

rdsEndpoint: rds.bj.baidubce.com
scsEndpoint: redis.bj.baidubce.com
gaiaDBEndpoint: gaiadb.bj.baidubce.com
stsEndpoint: http://sts.bj.iam.sdns.baidu.com:8586
eipEndpoint: eip.bj.baidubce.com
vpcEndpoint: http://vpc.bj.bce-internal.baidu.com
blbEndpoint: blb.bj.baidubce.com
cceEndpoint: cce.bj.baidubce.com
iamEndpoint: http://iam.bj.bce-internal.baidu.com
userSettingEndpoint: http://settings.bce-internal.baidu.com
privateZoneEndpoint: http://privatezone.baidubce.com
bosEndpoint: bj.bcebos.com
dnsEndpoint: http://bcedns.baidu-int.com
neutronEndpoint: http://neutron-a.bj.bce-internal.baidu.com:9696
rdnsEndpoint: http://rdns-bce.sdns.baidu.com
vpcDnsEndpoint: http://bcc.bj.baidubce.com
bccEndpoint: bcc.bj.baidubce.com
certificateEndpoint: http://certificate.baidubce.com
domainCheckEndpoint: http://domain-check.bce-internal.baidu.com

resourceManageEndpoint: http://resource-manager.bce-billing.baidu-int.com:8671

dnsToken: testtoken
region: bj
roleName: BceServiceRole_csm

serviceName: csm
servicePassword: srIkFXOwnRiPavuKtX2k8EiGY5vlFbTb

mysqlConnection: REPLACEME_MYSQL_CONNECTION
devopsConnection:

pnetDNSServer: ************
inetDNSServer: ns1.baidubce.com


billing:
  roleName: BceServiceRole_ccm
  serviceName: ccm
  servicePassword: REPLACEME_ECCR_SERVICE_PASSWORD
  accessKey: REPLACEME_ECCR_ACCESSKEY
  secretKey: REPLACEME_ECCR_SECRETKEY

kafkaOptions:
  brokerEndpoint: kafka.bj.baidubce.com:9091
  caPemPath: /home/<USER>/config/cert/kafka/ca.pem
  clientKeyPath: /home/<USER>/config/cert/kafka/client.key
  clientPemPath: /home/<USER>/config/cert/kafka/client.pem
  consumerGroupID: ccr
redis:
  engineVersion: "6.0"
  spec:
    advanced:
      nodeType: cache.n1.medium
    basic:
      nodeType: cache.n1.medium
    standard:
      nodeType: cache.n1.medium
  subnetID: sbn-c41m80sbu6bp
  zoneName: cn-bj-d