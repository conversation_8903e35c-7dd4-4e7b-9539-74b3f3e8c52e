package clientset

import (
	"fmt"
	"github.com/baidubce/bce-sdk-go/services/gaiadb"

	"github.com/baidubce/bce-sdk-go/services/scs"

	"time"

	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/baidubce/bce-sdk-go/services/rds"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcd"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/blb"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bosinterface"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/privatezone"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/kafka"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models/devops"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type ClientSetInterface interface {
	IAMClient() *iam.Client
	RestConfig(clusterID string) (*rest.Config, error)
	BosClientForAccount(accountId, userId, endpoint string) (bosinterface.BosInterface, error)
	VpcClientFromAccount(accountId, userId, endpoint string) (*vpc.Client, error)
	BccClientFromAccount(accountId, userId, endpoint string) (*bcc.Client, error)
	EipClientFromAccount(accountId, userId, endpoint string) (*eip.Client, error)
	VpcDnsClientFromAccount(accountId, userId, endpoint string) (dns.VpcDnsClientInterface, error)
	GaiaDBClient() *gaiadb.Client
	RdsClient() *rds.Client
	ScsClient() *scs.Client
	EipClient() *eip.Client
	BlbClient() *blb.Client
	Usersetting() usersetting.ClientInterface
	SqlClient() *models.Client
	DevopsClient() *devops.Client
	DnsClient() *dns.Client
	VpcDnsClient() *dns.VpcDnsClient
	ResourceClient() billing.ResourceClientInterface
	Producer() kafka.ProducerInterface
	VpcID(clusterID string) (string, error)
	StsCredentialWithExpiredAt(accountId, username string) (*Credentials, error)
	CertClientForAccount(accountId, userId, endpoint string) (certificate.Interface, error)
	BcdClientForAccount(accountId, userId, endpoint string) (bcd.Interface, error)
	PrivateZoneClientForAccount(accountID, userID, endpoint string) (privatezone.Interface, error)
}

var _ ClientSetInterface = &ClientSet{}

type Credentials struct {
	*auth.BceCredentials
	ExpiredAt time.Time
}

type ClientSet struct {
	stsClient sts.ClientInterface

	gaiaDBClient *gaiadb.Client
	rdsClient    *rds.Client
	scsClient    *scs.Client
	cceClient    *cce.Client
	iamClient    *iam.Client
	eipClient    *eip.Client
	blbClient    *blb.Client
	userSetting  usersetting.ClientInterface
	sqlClient    *models.Client

	dnsClient    *dns.Client
	vpcDnsClient *dns.VpcDnsClient

	devopsClient *devops.Client

	resourceClient billing.ResourceClientInterface

	producer kafka.ProducerInterface
}

func NewClientSet(config *conf.Config) (ClientSetInterface, error) {
	var (
		stsCli         *sts.Client
		gaiaDBCli      *gaiadb.Client
		rdsCli         *rds.Client
		scsCli         *scs.Client
		cceCli         *cce.Client
		eipCli         *eip.Client
		blbCli         *blb.Client
		usersettingCli usersetting.ClientInterface
		sqlCli         *models.Client
		devopsCli      *devops.Client
		resourceClient billing.ResourceClientInterface
		producer       kafka.ProducerInterface
		err            error
	)

	if config.MustSTS() {
		stsCli, err = sts.NewClient(config.STSEndpoint, config.IAMEndpoint, config.ServiceName, config.ServicePassword, config.RoleName)
		if err != nil {
			return nil, err
		}
	}

	if config.MustGaiaDB() {
		gaiaDBCli, err = gaiadb.NewClient(config.AccessKey, config.SecretKey, config.GaiaDBEndpoint)
		if err != nil {
			return nil, err
		}
	}

	if config.MustRds() {
		rdsCli, err = rds.NewClient(config.AccessKey, config.SecretKey, config.RDSEndpoint)
		if err != nil {
			return nil, err
		}
	}

	if config.MustSCS() {
		scsCli, err = scs.NewClient(config.AccessKey, config.SecretKey, config.SCSEndpoint)
		if err != nil {
			return nil, err
		}
	}

	cceCli, err = cce.NewClient(config.AccessKey, config.SecretKey, config.CCEEndpoint)
	if err != nil {
		return nil, err
	}

	if config.MustEIP() {
		eipCli, err = eip.NewClient(config.AccessKey, config.SecretKey, config.EIPEndpoint)
		if err != nil {
			return nil, err
		}
	}

	if config.MustBLB() {
		blbCli, err = blb.NewClient(config.AccessKey, config.SecretKey, config.BLBEndpoint)
		if err != nil && config.MustBLB() {
			return nil, err
		}
	}

	if config.MustUserSetting() {
		usersettingCli, err = usersetting.NewClient(config.AccessKey, config.SecretKey, config.UserSettingEndpoint)
		if err != nil && config.MustUserSetting() {
			return nil, err
		}
	}

	if config.MustSQL() {
		sqlCli, err = models.NewClient(nil, config.MysqlConnection)
		if err != nil && config.MustSQL() {
			return nil, err
		}

		devopsCli, err = devops.NewClient(nil, config.DevopsConnection)
		if err != nil {
			return nil, err
		}
	}

	if config.MustBilling() {
		resourceClient, err = billing.NewResourceClient(config.Billing.AccessKey, config.Billing.SecretKey, config.ResourceManageEndpoint)
		if err != nil {
			return nil, fmt.Errorf("create resource client failed: %w", err)
		}
	}

	if config.MustResgroup() {
		producer = kafka.NewProducer()
		if err != nil {
			return nil, fmt.Errorf("create kafka client failed: %w", err)
		}
	}

	internalIamCli := &iam.Client{BceClient: stsCli.IAM()}

	vpcDnsClient, err := dns.NewVpcDnsClient(config.AccessKey, config.SecretKey, config.VPCDNSEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create vpc dns client failed: %w", err)
	}

	return &ClientSet{
		stsClient:      stsCli,
		iamClient:      internalIamCli,
		gaiaDBClient:   gaiaDBCli,
		rdsClient:      rdsCli,
		scsClient:      scsCli,
		cceClient:      cceCli,
		eipClient:      eipCli,
		blbClient:      blbCli,
		userSetting:    usersettingCli,
		sqlClient:      sqlCli,
		devopsClient:   devopsCli,
		dnsClient:      dns.NewClient(config.DNSEndpoint, config.DNSToken, config.RDNSEndpoint),
		vpcDnsClient:   vpcDnsClient,
		resourceClient: resourceClient,
		producer:       producer,
	}, nil
}

func (c *ClientSet) IAMClient() *iam.Client {
	return c.iamClient
}

func (c *ClientSet) RestConfig(clusterID string) (*rest.Config, error) {
	result, err := c.cceClient.GetKubeConfig(&cce.GetKubeConfigArgs{
		ClusterUuid: clusterID,
		Type:        cce.KubeConfigTypeInternal,
	})
	if err != nil {
		return nil, err
	}

	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(result.Data))
	if err != nil {
		return nil, err
	}

	return config, nil
}

// BosClientForAccount 获取指定账号的BosClient，并返回BosInterface接口对象和错误信息
// 参数：
//
//	accountId (string) - 账号ID
//	userId (string) - 用户ID
//	endpoint (string) - BOS服务端点地址
//
// 返回值：
//
//	bosinterface.BosInterface (interface) - BosInterface接口对象
//	error (error) - 如果创建BosClient或者获取STS凭据失败，则返回错误信息
func (c *ClientSet) BosClientForAccount(accountId, userId, endpoint string) (bosinterface.BosInterface, error) {
	cred, err := c.stsClient.GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		cred.AccessKeyId,
		cred.SecretAccessKey,
		cred.SessionToken,
	)
	if err != nil {
		return nil, fmt.Errorf("create session credential failed: %w", err)
	}

	bosCli, err := bos.NewClient(cred.AccessKeyId, cred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create bos client failed: %w", err)
	}

	bosCli.Config.Credentials = stsCredential

	return bosCli, nil
}

func (c *ClientSet) VpcClientFromAccount(accountId, userId, endpoint string) (*vpc.Client, error) {
	stsCred, err := c.stsCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	vpcCli, err := vpc.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create vpc client failed: %w", err)
	}

	vpcCli.Config.Credentials = stsCred
	return vpcCli, nil
}

func (c *ClientSet) BccClientFromAccount(accountId, userId, endpoint string) (*bcc.Client, error) {
	stsCred, err := c.stsCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	bccCli, err := bcc.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create bcc client failed: %w", err)
	}

	bccCli.Config.Credentials = stsCred
	return bccCli, nil
}

func (c *ClientSet) EipClientFromAccount(accountId, userId, endpoint string) (*eip.Client, error) {
	stsCred, err := c.stsCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	eipCli, err := eip.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create eip client failed: %w", err)
	}

	eipCli.Config.Credentials = stsCred
	return eipCli, nil

}

func (c *ClientSet) VpcDnsClientFromAccount(accountId, userId, endpoint string) (dns.VpcDnsClientInterface, error) {
	stsCred, err := c.stsCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	vpcDnsCli, err := dns.NewVpcDnsClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create vpc dns client failed: %w", err)
	}
	vpcDnsCli.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return vpcDnsCli, nil
}

func (c *ClientSet) GaiaDBClient() *gaiadb.Client {
	return c.gaiaDBClient
}

func (c *ClientSet) RdsClient() *rds.Client {
	return c.rdsClient
}

func (c *ClientSet) ScsClient() *scs.Client {
	return c.scsClient
}

func (c *ClientSet) EipClient() *eip.Client {
	return c.eipClient
}

func (c *ClientSet) BlbClient() *blb.Client {
	return c.blbClient
}

func (c *ClientSet) Usersetting() usersetting.ClientInterface {
	return c.userSetting
}

func (c *ClientSet) SqlClient() *models.Client {
	return c.sqlClient
}

func (c *ClientSet) DevopsClient() *devops.Client {
	return c.devopsClient
}

func (c *ClientSet) DnsClient() *dns.Client {
	return c.dnsClient
}

func (c *ClientSet) VpcDnsClient() *dns.VpcDnsClient {
	return c.vpcDnsClient
}

func (c *ClientSet) ResourceClient() billing.ResourceClientInterface {
	return c.resourceClient
}

func (c *ClientSet) Producer() kafka.ProducerInterface {
	return c.producer
}

func (c *ClientSet) VpcID(clusterID string) (string, error) {
	result, err := c.cceClient.GetCluster(clusterID)
	if err != nil {
		return "", err
	}

	return result.VpcId, nil
}

func (c *ClientSet) stsCredential(accountId, userId string) (*auth.BceCredentials, error) {
	cred, err := c.stsClient.GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		cred.AccessKeyId,
		cred.SecretAccessKey,
		cred.SessionToken,
	)
	if err != nil {
		return nil, fmt.Errorf("create session credential failed: %w", err)
	}

	return stsCredential, nil
}

func (c *ClientSet) StsCredentialWithExpiredAt(accountId, username string) (*Credentials, error) {
	// 获取userId
	listResp, err := c.iamClient.GetUsers(&iam.UserListRequest{
		DomainID: accountId,
		Name:     username,
	})
	if err != nil {
		return nil, fmt.Errorf("get user list failed: %s", err)
	}

	if len(listResp.UserList) == 0 {
		return nil, nil
	}

	if len(listResp.UserList) > 1 {
		return nil, fmt.Errorf("the users with nmae %v is not only one: %v", username, listResp.UserList)
	}

	user := listResp.UserList[0]

	if !user.Enabled {
		return nil, fmt.Errorf("user is disabled: %v", listResp.UserList[0])
	}

	cred, err := c.stsClient.GetCredential(accountId, user.ID)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		cred.AccessKeyId,
		cred.SecretAccessKey,
		cred.SessionToken,
	)
	if err != nil {
		return nil, fmt.Errorf("create session credential failed: %w", err)
	}

	credential := &Credentials{
		BceCredentials: stsCredential,
		ExpiredAt:      cred.Expiration,
	}
	return credential, nil
}

// CertClientForAccount 为指定账户和用户获取证书客户端。
func (c *ClientSet) CertClientForAccount(accountID, userID, endpoint string) (certificate.Interface, error) {
	stsCred, err := c.stsCredential(accountID, userID)
	if err != nil {
		return nil, err
	}

	certCli, err := certificate.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, stsCred.SessionToken, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create certificate client failed: %w", err)
	}

	return certCli, nil
}

// BcdClientForAccount 为指定账户和用户获取BCD客户端。
func (c *ClientSet) BcdClientForAccount(accountID, userID, endpoint string) (bcd.Interface, error) {
	stsCred, err := c.stsCredential(accountID, userID)
	if err != nil {
		return nil, err
	}

	bcdClient, err := bcd.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, stsCred.SessionToken, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create bcd client failed: %w", err)
	}

	return bcdClient, nil
}

// PrivateZoneClientForAccount 为指定账户和用户获取私有网络PrivateZone客户端
func (c *ClientSet) PrivateZoneClientForAccount(accountID, userID, endpoint string) (privatezone.Interface, error) {
	stsCred, err := c.stsCredential(accountID, userID)
	if err != nil {
		return nil, err
	}
	privateZoneClient, err := privatezone.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, stsCred.SessionToken, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create privatezone client failed: %w", err)
	}

	return privateZoneClient, nil
}
