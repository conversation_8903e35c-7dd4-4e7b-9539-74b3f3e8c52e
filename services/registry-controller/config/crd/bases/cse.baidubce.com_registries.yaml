---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: registries.cse.baidubce.com
spec:
  group: cse.baidubce.com
  names:
    kind: Registry
    listKind: RegistryList
    plural: registries
    singular: registry
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: PHASE
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: Registry is the Schema for the registries API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: RegistrySpec defines the desired state of Registry
            properties:
              accountID:
                type: string
              args:
                properties:
                  apiEurekaCustomDciClass:
                    type: string
                  apiEurekaCustomDciName:
                    type: string
                  apiEurekaDeltaIntervalSeconds:
                    format: int32
                    type: integer
                  clientOpen:
                    description: 客户端鉴权配置
                    type: boolean
                  clientStrict:
                    type: boolean
                  consoleEnable:
                    type: boolean
                  jobDeleteInstanceUnhealthyEnable:
                    type: boolean
                  jobDeleteInstanceUnhealthyIntervalMinutes:
                    format: int32
                    type: integer
                  jobDeleteServiceUnreferencedEnable:
                    type: boolean
                  jobDeleteServiceUnreferencedIntervalMinutes:
                    format: int32
                    type: integer
                  monitorCpromId:
                    type: string
                  monitorCpromToken:
                    type: string
                  monitorEnable:
                    type: boolean
                  namespaceAutoCreate:
                    type: boolean
                type: object
              esgID:
                type: string
              esgName:
                type: string
              publicAccess:
                description: 公网访问配置
                properties:
                  bandwidthMbps:
                    type: integer
                  billingMethod:
                    type: string
                  customDomain:
                    type: string
                  enabled:
                    type: boolean
                  whiteList:
                    items:
                      type: string
                    type: array
                required:
                - enabled
                type: object
              release:
                type: string
              serverSpec:
                properties:
                  cpu:
                    type: string
                  memory:
                    type: string
                  replicas:
                    format: int32
                    type: integer
                type: object
              subnetID:
                type: string
              userID:
                type: string
              vpcID:
                type: string
            type: object
          status:
            description: RegistryStatus defines the observed state of Registry
            properties:
              endpointList:
                items:
                  properties:
                    id:
                      type: string
                    ip:
                      type: string
                    type:
                      type: string
                  required:
                  - id
                  - ip
                  - type
                  type: object
                type: array
              lastPhaseTime:
                format: date-time
                type: string
              lastProbeTime:
                format: date-time
                type: string
              message:
                type: string
              phase:
                description: CCRPhase is a label for the condition of a CCR instance
                  at current time
                type: string
              publicAccess:
                description: 公网访问状态
                properties:
                  bandwidthMbps:
                    type: integer
                  billingMethod:
                    type: string
                  customDomain:
                    type: string
                  enabled:
                    type: boolean
                  publicDomain:
                    type: string
                  publicIP:
                    type: string
                  reason:
                    type: string
                  securityGroupId:
                    type: string
                  status:
                    type: string
                  whiteList:
                    items:
                      type: string
                    type: array
                required:
                - enabled
                type: object
              reason:
                type: string
              release:
                type: string
              serverBLB:
                properties:
                  bRegionIP:
                    type: string
                  id:
                    type: string
                  ip:
                    type: string
                  port:
                    type: integer
                required:
                - bRegionIP
                - id
                - ip
                - port
                type: object
              serverConfigVersion:
                type: string
              store:
                properties:
                  mysql:
                    properties:
                      address:
                        type: string
                      database:
                        type: string
                      password:
                        type: string
                      port:
                        type: integer
                      user:
                        type: string
                    required:
                    - user
                    type: object
                  redis:
                    properties:
                      address:
                        type: string
                      database:
                        type: string
                      password:
                        type: string
                      port:
                        type: integer
                      user:
                        type: string
                    required:
                    - user
                    type: object
                type: object
              token:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources: {}
