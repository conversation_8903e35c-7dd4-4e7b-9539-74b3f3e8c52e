package registry

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	csev1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/cse/v1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/registry-controller/metrics"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/registry-controller/store/gaiadb"
)

const (
	// todo 改成配置
	serviceName   = "registry-server"
	vmagentName   = "vmagent"
	configMapName = "polaris-server-config"
	// todo 改成配置，跟 register-center service 里的 httpPort 一致
	serverBLBPort             = 43298
	annotationLoadBalancerID  = "service.beta.kubernetes.io/cce-load-balancer-id"
	annotationLoadBalancerVIP = "service.beta.kubernetes.io/cce-load-balancer-vip"

	// 中间状态超过多少秒后修改状态为Failed
	statusTimeoutSecond = 60 * 30
)

type RegisterCenterHandler struct {
	client    client.Client
	dbs       *sync.Map
	mgr       manager.Manager
	storePool *gaiadb.Pool
	region    string
}

func NewRegisterCenterHandler(client client.Client, mgr manager.Manager, storePool *gaiadb.Pool, region string) *RegisterCenterHandler {
	return &RegisterCenterHandler{
		client:    client,
		dbs:       new(sync.Map),
		mgr:       mgr,
		storePool: storePool,
		region:    region,
	}
}

func isPhaseNeedCheckTimeout(phase ccrv1alpha1.CCRPhase) bool {
	return phase == ccrv1alpha1.CCRCreating || phase == ccrv1alpha1.CCRCalibrating
}

func (rc *RegisterCenterHandler) Handle(ctx context.Context, object *csev1.Registry) error {
	obj := object.DeepCopy()
	logger := log.FromContext(ctx)
	logger.Info("start to handle", "object", obj.GetName())

	// 中间状态持续超过statusTimeoutSecond秒修改状态为Failed
	if object.Status.LastPhaseTime != nil && isPhaseNeedCheckTimeout(object.Status.Phase) {
		if object.Status.LastPhaseTime.Add(time.Second * statusTimeoutSecond).Before(time.Now()) {
			message := fmt.Sprintf("lastPhase:%v, lastPhaseTime:%v", object.Status.Phase, object.Status.LastPhaseTime)
			rc.updatePhase(ctx, obj, ccrv1alpha1.CCRFailed)
			return rc.updateReason(ctx, obj, "PhaseTimeout", message)
		}
	}

	switch {
	case obj.DeletionTimestamp != nil:
		return rc.handleDelete(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.CCRPending:
		return rc.toCreating(ctx, object)
	case obj.Status.Phase == ccrv1alpha1.CCRCreating:
		return rc.handleCreate(ctx, object)
	case obj.Status.Phase == ccrv1alpha1.CCRRunning:
		return rc.handleUpdate(ctx, object)
	case obj.Status.Phase == ccrv1alpha1.CCRCalibrating:
		return rc.ensureCalibratingDone(ctx, object)
	}

	return nil
}

func ownerReferenceFor(obj client.Object) *metav1.OwnerReference {
	trueFlag := true
	return &metav1.OwnerReference{
		APIVersion:         obj.GetObjectKind().GroupVersionKind().GroupVersion().Identifier(),
		Kind:               obj.GetObjectKind().GroupVersionKind().Kind,
		Name:               obj.GetName(),
		UID:                obj.GetUID(),
		Controller:         &trueFlag,
		BlockOwnerDeletion: &trueFlag,
	}
}

func (rc *RegisterCenterHandler) updateReason(ctx context.Context, object *csev1.Registry, reason string, message string) error {
	logger := log.FromContext(ctx)
	object.Status.Reason = reason
	object.Status.Message = message
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime

	object.Status.Release = object.Spec.Release
	err := rc.client.Update(ctx, object)
	if err != nil {
		logger.Error(err, "update status failed")
	}
	return err
}

func (rc *RegisterCenterHandler) updatePhase(ctx context.Context, object *csev1.Registry, phase ccrv1alpha1.CCRPhase) {
	if object.Status.Phase == phase {
		return
	}

	duration := .0
	if object.Status.LastPhaseTime != nil {
		duration = time.Since(object.Status.LastPhaseTime.Time).Seconds()
	}
	metrics.PhaseChangeDurationSeconds.
		WithLabelValues(object.GetName(), string(object.Status.Phase), string(phase)).Observe(duration)
	metrics.CseInstanceStatusPhase.WithLabelValues(object.GetName(), string(object.Status.Phase)).Set(0)
	metrics.CseInstanceStatusPhase.WithLabelValues(object.GetName(), string(phase)).Set(1)

	object.Status.Phase = phase
	object.Status.LastPhaseTime = &metav1.Time{Time: time.Now()}
}

func (c *RegisterCenterHandler) setFinalizer(ctx context.Context, object *csev1.Registry) (bool, error) {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		finalizers = make([]string, 0)
	}

	for _, v := range finalizers {
		if v == RCFinalizer {
			return true, nil
		}
	}

	finalizers = append(finalizers, RCFinalizer)
	object.SetFinalizers(finalizers)

	return false, c.client.Update(ctx, object)
}

func (c *RegisterCenterHandler) removeFinalizer(ctx context.Context, object *csev1.Registry) error {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		return nil
	}

	reservedFinalizer := make([]string, 0)
	for _, v := range finalizers {
		if v != RCFinalizer {
			reservedFinalizer = append(reservedFinalizer, v)
		}
	}

	object.SetFinalizers(reservedFinalizer)
	return c.client.Update(ctx, object)
}

func (c *RegisterCenterHandler) currentServerConfigVersion(ctx context.Context, object *csev1.Registry) (string, error) {
	var cm corev1.ConfigMap
	if err := c.client.Get(context.Background(), client.ObjectKey{Namespace: object.GetName(), Name: configMapName}, &cm); err != nil {
		return "", fmt.Errorf("get cm failed: %v", err)
	}
	return cm.GetResourceVersion(), nil
}

func (c *RegisterCenterHandler) updateServer(ctx context.Context, object *csev1.Registry) error {
	var sts appv1.StatefulSet
	if err := c.client.Get(context.Background(), client.ObjectKey{Namespace: object.GetName(), Name: serviceName}, &sts); err != nil {
		return fmt.Errorf("get sts failed: %v", err)
	}

	if sts.Spec.Template.Annotations == nil {
		sts.Spec.Template.Annotations = map[string]string{}
	}
	sts.Spec.Template.Annotations["register-center.baidubce.com/lastUpdateTime"] = strconv.FormatInt(time.Now().UnixNano(), 10)
	if err := c.client.Update(context.Background(), &sts); err != nil {
		return fmt.Errorf("update sts failed: %v", err)
	}
	return nil
}

// getRegistryConfig 获取registry chart中需要动态渲染的内容
func (c *RegisterCenterHandler) getRegistryConfig(object *csev1.Registry) map[string]interface{} {
	slaveAddress := ""
	if proxyAddress := object.Status.Store.MySQL.GetProxyAddress(); proxyAddress != "" {
		slaveAddress = fmt.Sprintf("%s:%d", proxyAddress, object.Status.Store.MySQL.Port)
	}
	values := map[string]interface{}{
		"polaris": map[string]interface{}{
			"limit": map[string]interface{}{
				"cpu":    object.Spec.ServerSpec.CPU,
				"memory": object.Spec.ServerSpec.Memory,
			},
			"healthChecker": map[string]interface{}{
				"redis": map[string]interface{}{
					"address": fmt.Sprintf("%s:%d", object.Status.Store.Redis.Address, object.Status.Store.Redis.Port),
					"passwd":  object.Status.Store.Redis.Password,
				},
			},
			"replicaCount": object.Spec.ServerSpec.Replicas,
			"storage": map[string]interface{}{
				"db": map[string]interface{}{
					"address":      fmt.Sprintf("%s:%d", object.Status.Store.MySQL.GetMasterAddress(), object.Status.Store.MySQL.Port),
					"slaveAddress": slaveAddress,
					"password":     object.Status.Store.MySQL.Password,
					"name":         c.storePool.FormatDatabase(object.Name),
					"user":         object.Status.Store.MySQL.User,
				},
			},
			// 添加 auth 配置
			"auth": map[string]interface{}{
				"consoleOpen":  true,  // 默认值
				"clientOpen":   false, // 默认值
				"clientStrict": false, // 默认值
			},
		},
		"installation": map[string]interface{}{
			"namespace": object.GetName(),
		},
		"monitor": map[string]interface{}{
			"region":         c.region,
			"remoteWriteURL": fmt.Sprintf("https://cprom.%s.baidubce.com/insert/prometheus/api/v1/write", c.region),
		},
		"owner": object.Spec.AccountID,
	}

	values["release"] = object.Spec.Release

	if object.Spec.Args == nil {
		return values
	}
	args := make(map[string]interface{})
	if object.Spec.Args.ApiEurekaCustomDciName != "" {
		args["dataCenterInfoName"] = object.Spec.Args.ApiEurekaCustomDciName
	}
	if object.Spec.Args.ApiEurekaCustomDciClass != "" {
		args["dataCenterInfoClass"] = object.Spec.Args.ApiEurekaCustomDciClass
	}
	if object.Spec.Args.ApiEurekaDeltaIntervalSeconds != nil {
		args["deltaExpireInterval"] = *object.Spec.Args.ApiEurekaDeltaIntervalSeconds
	}
	if object.Spec.Args.JobDeleteInstanceUnhealthyEnable != nil {
		args["deleteUnHealthyInstanceEnable"] = *object.Spec.Args.JobDeleteInstanceUnhealthyEnable
	}
	if object.Spec.Args.JobDeleteInstanceUnhealthyIntervalMinutes != nil {
		args["deleteUnHealthyInstanceTimeout"] = fmt.Sprintf("%dm", *object.Spec.Args.JobDeleteInstanceUnhealthyIntervalMinutes)
	}
	if object.Spec.Args.JobDeleteServiceUnreferencedEnable != nil {
		args["deleteEmptyAutoCreatedServiceEnable"] = *object.Spec.Args.JobDeleteServiceUnreferencedEnable
	}
	if object.Spec.Args.JobDeleteServiceUnreferencedIntervalMinutes != nil {
		args["deleteEmptyAutoCreatedServiceTimeout"] = fmt.Sprintf("%dm", *object.Spec.Args.JobDeleteServiceUnreferencedIntervalMinutes)
	}
	if object.Spec.Args.NamespaceAutoCreate != nil {
		args["namespaceAutoCreate"] = *object.Spec.Args.NamespaceAutoCreate
	}
	if object.Spec.Args.MonitorEnable != nil {
		if *object.Spec.Args.MonitorEnable {
			args["monitorReplicas"] = 5
		} else {
			args["monitorReplicas"] = 0
		}
	}
	if object.Spec.Args.MonitorCpromId != "" {
		args["monitorCpromId"] = object.Spec.Args.MonitorCpromId
	}
	if object.Spec.Args.MonitorCpromToken != "" {
		token := object.Spec.Args.MonitorCpromToken
		args["monitorCpromToken"] = strings.TrimSpace(strings.TrimPrefix(token, "Bearer"))
	}
	if object.Spec.Args.ConsoleEnable != nil {
		if *object.Spec.Args.ConsoleEnable {
			args["consoleReplicas"] = 2
		} else {
			args["consoleReplicas"] = 0
		}
	}

	// 处理客户端鉴权配置
	authConfig := values["polaris"].(map[string]interface{})["auth"].(map[string]interface{})

	if object.Spec.Args.ClientOpen != nil {
		authConfig["clientOpen"] = *object.Spec.Args.ClientOpen
	}

	if object.Spec.Args.ClientStrict != nil {
		authConfig["clientStrict"] = *object.Spec.Args.ClientStrict
	}

	if !(strings.HasPrefix(object.Spec.Release, "1.0") ||
		strings.HasPrefix(object.Spec.Release, "1.1") ||
		strings.HasPrefix(object.Spec.Release, "1.2")) {
		// 实例版本大于等于1.3
		values["service"] = map[string]interface{}{
			"loadBalancerId": object.Status.ServerBLB.ID,
			//"vip":            object.Status.ServerBLB.BRegionIP,
		}
	}
	values["args"] = args
	return values
}

func getHash(i interface{}) (string, error) {
	bytes, err := json.Marshal(i)
	if err != nil {
		return "", err
	}
	sort.Slice(bytes, func(i, j int) bool {
		return bytes[i] < bytes[j]
	})
	hash := sha256.Sum256(bytes)
	return hex.EncodeToString(hash[:]), err
}
