package registry

import (
	"context"
	"fmt"
	apierrors "k8s.io/apimachinery/pkg/api/errors"

	pkgrelease "helm.sh/helm/v3/pkg/release"
	appv1 "k8s.io/api/apps/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	csev1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/cse/v1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/helm"
)

func (c *RegisterCenterHandler) handleUpdate(ctx context.Context, object *csev1.Registry) error {
	// 1. 处理安全组更新
	esgUpdated, err := c.ensureEsg(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "calibrateEsgFailed", err.Error())
		return err
	}

	// 2. 处理服务器配置更新
	serverConfigUpdated, err := c.ensureServerConfig(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "calibrateServerConfigFailed", err.Error())
		return err
	}

	// 3. 处理公网访问配置更新（新增）
	publicAccessUpdated, err := c.ensurePublicAccessConfig(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "calibratePublicAccessFailed", err.Error())
		return err
	}

	// 4. 同步公网访问状态（新增）
	// 获取CNCNetwork对象以同步状态
	var netObj *ccrv1alpha1.CNCNetwork
	if object.Spec.PublicAccess != nil {
		netObj = &ccrv1alpha1.CNCNetwork{}
		err := c.client.Get(ctx, client.ObjectKey{
			Namespace: object.GetName(),
			Name:      object.GetName(),
		}, netObj)
		if err != nil && !apierrors.IsNotFound(err) {
			_ = c.updateReason(ctx, object, "getCNCNetworkFailed", err.Error())
			return err
		}
		if apierrors.IsNotFound(err) {
			netObj = nil // CNCNetwork不存在时设为nil
		}
	}

	if err := c.syncPublicAccessStatus(ctx, object, netObj); err != nil {
		_ = c.updateReason(ctx, object, "syncPublicAccessStatusFailed", err.Error())
		return err
	}

	// 修复：如果只是状态同步，也需要保存状态更新
	if err := c.client.Status().Update(ctx, object); err != nil {
		_ = c.updateReason(ctx, object, "updateStatusFailed", err.Error())
		return err
	}

	if esgUpdated || serverConfigUpdated || publicAccessUpdated {
		c.updatePhase(ctx, object, ccrv1alpha1.CCRCalibrating)
		_ = c.updateReason(
			ctx,
			object,
			"waitCalibrating",
			fmt.Sprintf("esgUpdated:%v,serverUpdated:%v,publicAccessUpdated:%v", esgUpdated, serverConfigUpdated, publicAccessUpdated),
		)
		return fmt.Errorf("waitCalibrating")
	}
	return nil
}

func (c *RegisterCenterHandler) ensureServerConfig(ctx context.Context, object *csev1.Registry) (bool, error) {
	values := c.getRegistryConfig(object)
	// expectRegistryCrdVersion 当前registry spec的hash值
	expectRegistryCrdVersion, err := getHash(values)
	if err != nil {
		return false, err
	}
	// currentRegistryCrdVersion 当前registry status的hash值
	currentRegistryCrdVersion := object.Status.GetRegistryCrdVersion()
	if expectRegistryCrdVersion == currentRegistryCrdVersion {
		// 两者一样，不需要调整，直接退出
		return false, nil
	}
	object.Status.WriteConfigServerVersion("", expectRegistryCrdVersion)

	st, err := helm.Status(ctx, object.Name, object.Name, c.mgr)
	if err != nil {
		return false, fmt.Errorf("helm status failed: %v", err)
	}

	if err = helm.Upgrade(ctx, object.GetName(), object.GetName(), ChartPath, values, c.mgr); err != nil {
		return false, fmt.Errorf("upgrade helm failed: %v", err)
	}
	if st == pkgrelease.StatusFailed {
		return false, fmt.Errorf("chart status is failed")
	}

	// helm upgrade后，确认configmap是否变化
	serverConfigVersion, err := c.currentServerConfigVersion(ctx, object)
	if err != nil {
		return false, fmt.Errorf("get cm failed: %v", err)
	}
	if serverConfigVersion == object.Status.GetServerConfigVersion() {
		// registry需要调整，但是server的configmap没有变化, 说明这里仅是vmagent调整了
		return true, nil
	}
	object.Status.WriteConfigServerVersion(serverConfigVersion, "")

	// server configmap有变化，需要重启server应用配置更新
	if err = c.updateServer(ctx, object); err != nil {
		return false, fmt.Errorf("update server failed: %v", err)
	}
	return true, nil
}

func (c *RegisterCenterHandler) ensureEsg(ctx context.Context, object *csev1.Registry) (bool, error) {
	if object.Spec.EsgID == "" {
		return false, nil
	}
	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil {
		return false, fmt.Errorf("get cncnetwork %v failed: %w", object.GetName(), err)
	}

	if len(netObj.Spec.PrivateLinks) != 1 {
		return false, fmt.Errorf("unexpectPrivateLinkStatus, expect:%v, actual:%v", 1, len(netObj.Spec.PrivateLinks))
	}

	// 比较registry的esgId和cncnetwork的esgId，判断是否需要重新绑定安全组
	actualEsgId := netObj.Spec.PrivateLinks[0].EsgID
	expectEsgId := object.Spec.EsgID
	if actualEsgId == expectEsgId {
		return false, nil
	}

	netObj.Spec.PrivateLinks[0].EsgID = expectEsgId
	if err = c.client.Update(ctx, &netObj); err != nil {
		return false, err
	}
	return true, nil
}

func (c *RegisterCenterHandler) ensureCalibratingDone(ctx context.Context, object *csev1.Registry) error {
	// 1. 确认server状态ok
	err := c.ensureServerConfigCalibrateDone(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "ensureServerConfigCalibrateDone", err.Error())
		return err
	}
	// 2. 确认esg状态ok
	err = c.ensureEsgCalibrateDone(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "ensureEsgCalibrateDone", err.Error())
		return err
	}

	// 3. 同步公网访问状态（修复：确保Calibrating完成后也进行状态同步）
	var netObj *ccrv1alpha1.CNCNetwork
	if object.Spec.PublicAccess != nil {
		netObj = &ccrv1alpha1.CNCNetwork{}
		err := c.client.Get(ctx, client.ObjectKey{
			Namespace: object.GetName(),
			Name:      object.GetName(),
		}, netObj)
		if err != nil && !apierrors.IsNotFound(err) {
			_ = c.updateReason(ctx, object, "getCNCNetworkFailed", err.Error())
			return err
		}
		if apierrors.IsNotFound(err) {
			netObj = nil // CNCNetwork不存在时设为nil
		}
	}

	if err := c.syncPublicAccessStatus(ctx, object, netObj); err != nil {
		_ = c.updateReason(ctx, object, "syncPublicAccessStatusFailed", err.Error())
		return err
	}

	// 修复：确保状态更新被保存到etcd
	if err := c.client.Status().Update(ctx, object); err != nil {
		_ = c.updateReason(ctx, object, "updateStatusFailed", err.Error())
		return err
	}

	c.updatePhase(ctx, object, ccrv1alpha1.CCRRunning)
	return c.updateReason(ctx, object, "", "")
}

func (c *RegisterCenterHandler) ensureServerConfigCalibrateDone(ctx context.Context, object *csev1.Registry) error {
	ensureStsReplicas := func(stsName string) error {
		// 1. 获取sts
		var sts appv1.StatefulSet
		if err := c.client.Get(context.Background(), client.ObjectKey{Namespace: object.GetName(), Name: stsName}, &sts); err != nil {
			return fmt.Errorf("get sts failed: %v", err)
		}

		// 2. 确认sts副本状态
		if sts.Status.UpdatedReplicas != sts.Status.Replicas {
			return fmt.Errorf("wait server ready, replicas: %v, updated: %v", sts.Status.Replicas, sts.Status.UpdatedReplicas)
		}
		return nil
	}

	// 1. 确认registry-server副本更新ok
	if err := ensureStsReplicas(serviceName); err != nil {
		return err
	}

	// 2. 确认vmagent副本更新ok
	if err := ensureStsReplicas(vmagentName); err != nil {
		return err
	}

	return nil
}

func (c *RegisterCenterHandler) ensureEsgCalibrateDone(ctx context.Context, object *csev1.Registry) error {
	// 1. 获取服务网卡
	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil {
		return err
	}
	if len(netObj.Status.LinkStatus) != 1 {
		return fmt.Errorf("get snic failed")
	}
	linkStatus := netObj.Status.LinkStatus[0]

	// 2. 确认服务网卡绑定的安全组
	if linkStatus.Status != ccrv1alpha1.PrivateLinkStatusCreated || linkStatus.EsgID != object.Spec.EsgID {
		return fmt.Errorf("wait binding esg")
	}

	return nil
}

// ensurePublicAccessConfig 确保公网访问配置同步到CNCNetwork
func (c *RegisterCenterHandler) ensurePublicAccessConfig(ctx context.Context, object *csev1.Registry) (bool, error) {
	// 1. 获取当前的CNCNetwork
	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil {
		return false, fmt.Errorf("get cncnetwork %v failed: %w", object.GetName(), err)
	}

	// 2. 构建期望的CNCNetwork配置
	expectedSpec := c.buildCNCNetworkSpec(object)

	// 3. 比较公网访问配置是否需要更新
	needUpdate := false

	// 检查EIPOn状态
	if netObj.Spec.PublicLink.EIPOn != expectedSpec.PublicLink.EIPOn {
		needUpdate = true
	}

	// 检查白名单配置
	if !equalNetworkWhiteItems(netObj.Spec.PublicLink.WhiteList, expectedSpec.PublicLink.WhiteList) {
		needUpdate = true
	}

	// 检查计费方式配置
	if netObj.Spec.PublicLink.BillingMethod != expectedSpec.PublicLink.BillingMethod {
		needUpdate = true
	}

	// 检查带宽配置
	if netObj.Spec.PublicLink.BandwidthMbps != expectedSpec.PublicLink.BandwidthMbps {
		needUpdate = true
	}

	// 检查域名配置
	if netObj.Spec.Domain.PublicDomain != expectedSpec.Domain.PublicDomain {
		needUpdate = true
	}

	// 4. 如果需要更新，则更新CNCNetwork
	if needUpdate {
		netObj.Spec.PublicLink = expectedSpec.PublicLink
		netObj.Spec.Domain = expectedSpec.Domain

		if err = c.client.Update(ctx, &netObj); err != nil {
			return false, fmt.Errorf("update cncnetwork public access config failed: %w", err)
		}
		return true, nil
	}

	return false, nil
}

// equalNetworkWhiteItems 比较两个NetworkWhiteItem切片是否相等
func equalNetworkWhiteItems(a, b []ccrv1alpha1.NetworkWhiteItem) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v.IPCidr != b[i].IPCidr {
			return false
		}
	}
	return true
}
