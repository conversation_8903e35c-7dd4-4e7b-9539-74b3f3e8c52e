apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vmagent
  namespace: {{ .Values.installation.namespace }}
spec:
  replicas: {{ .Values.args.monitorReplicas }}
  selector:
    matchLabels:
      app: vmagent
  serviceName: vmagent
  template:
    metadata:
      annotations:
        prometheus.io/port: '{{ .Values.monitor.port }}'
        prometheus.io/scrape: 'true'
      labels:
        app: vmagent
    spec:
      containers:
        - args:
            - '-envflag.enable=true'
            - '-promscrape.cluster.membersCount={{ .Values.args.monitorReplicas }}'
            - '-promscrape.cluster.memberNum=$(promscrape_cluster_memberPodName)'
            - '-promscrape.configCheckInterval=30s'
            - '-promscrape.streamParse=true'
            - '-promscrape.maxScrapeSize=150MiB'
            - '-remoteWrite.label=region={{ .Values.monitor.region }}'
            - '-promscrape.config=/etc/prometheus/config/prometheus.yaml'
            - '-promscrape.suppressDuplicateScrapeTargetErrors=true'
            - >-
              -remoteWrite.url={{ .Values.monitor.remoteWriteURL }}
            - '-promscrape.config.strictParse=false'
            - '-remoteWrite.headers=InstanceId:{{ .Values.args.monitorCpromId }}'
            - >-
              -remoteWrite.bearerToken={{ .Values.args.monitorCpromToken }}
          env:
            - name: REPLICA_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: promscrape_cluster_memberPodName
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
          image: {{ .Values.monitor.image }}
          imagePullPolicy: Always
          name: vmagent
          ports:
            - containerPort: {{ .Values.monitor.port }}
              name: http
              protocol: TCP
          resources:
            limits:
              cpu: '4'
              memory: 8000Mi
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - mountPath: /etc/prometheus/config
              name: config
      serviceAccount: vmagent
      serviceAccountName: vmagent
      volumes:
        - configMap:
            defaultMode: 420
            name: vmagent-config
          name: config