# Default values for ccr-service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

kind: Deployment
hostNetwork: false
dnsPolicy: ClusterFirstWithHostNet

containerPort: 8500

replicaCount: 3

image:
  repository: registry.baidubce.com/ccr-image/ccr-service
  pullPolicy: IfNotPresent
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: ccr-service.image.tag

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

ccrPullSecret: ********************************************************************************************************************************************************************************************************

config:
  ak: REPLACEME_ACCESSKEY
  sk: REPLACEME_SECRETKEY
  listenAddress: ":8500"
  clusterId: xxx
  endpoint:
    ccr: xxx
    sts: xxx
    vpc: xxx
    cce: xxx
    iam: xxx
    facade: http://orderfacade.bce-console.sdns.baidu.com
    order: http://order.bce-internal.baidu.com
    resource: http://resource-manager.bce-billing.baidu-int.com:8671
    autoRenew: http://orderrenew.bce-console.sdns.baidu.com
    userSetting: http://settings.bce-internal.baidu.com
    bus: http://register.bce-console.sdns.baidu.com
    ccrService: xxx
    logicTag: http://taglogic.bj.bce-internal.baidu.com
    certificate: http://certificate.baidubce.com
    domaincheck: http://domain-check.bce-internal.baidu.com
    resourcegroup: http://res-manager.bce-console.baidu-int.com
  regionEndpoints:
    gz: "https://ccr.gz.baidubce.com"
    bj: "https://ccr.bj.baidubce.com"
    hkg: "https://ccr.hkg.baidubce.com"
    fwh: "https://ccr.fwh.baidubce.com"
    bd: "https://ccr.bd.baidubce.com"
    su: "https://ccr.su.baidubce.com"
    cd: "https://ccr.cd.baidubce.com"
    yq: "https://ccr.yq.baidubce.com"
  billing:
    roleName: BceServiceRole_eccr
    serviceName: eccr
    servicePassword: REPLACEME_ECCR_SERVICE_PASSWORD
    ak: REPLACEME_ECCR_ACCESSKEY
    sk: REPLACEME_ECCR_SECRETKEY
  region: xxx
  roleName: BceServiceRole_ccr
  serviceName: ccr
  servicePassword: REPLACEME_SERVICE_PASSWORD
  mysqlConnection: xxx
  registryAllowlist: hub.baidubce.com;iregistry.baidu-int.com;registry.baidubce.com
  checkUserFeatureACL: true


## Kubernetes configuration
## For minikube, set this to NodePort, elsewhere use LoadBalancer or ClusterIP
##
service:
  type: NodePort
  # HTTP Port
  port: 8500
  # nodePort
  nodePort: 8500
  # metrics Port
  metricsPort: 9117
  targetPort: 8500


## E2e-coverage service
## In order to fetch e2e coverage data across machines
e2eCoverage:
  enabled: false


## Configure the ingress resource that allows you to access the
## ccr-service installation. Set up the URL
## ref: http://kubernetes.io/docs/user-guide/ingress/
##
ingress:
  ## Set to true to enable ingress record generation
  enabled: false

  tls:

  ## When the ingress is enabled, a host pointing to this will be created
  hostname: ccr.gztest.baidubce.com


podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8500"

podSecurityContext: {}
  # fsGroup: 2000

securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/name
              operator: In
              values:
                - ccr-service
        topologyKey: "kubernetes.io/hostname"
