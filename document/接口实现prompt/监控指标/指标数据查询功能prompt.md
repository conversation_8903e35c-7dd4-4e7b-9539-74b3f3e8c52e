你是一个后端开发工程师 现在有一个新的需求 需要开发一个prometheus指标查询的接口，接口入口放在api-logical-aigw/cmd/csm/app/router/aigateway.go下具体url格式参照已有接口，该接口的具体实现需要调用远程存储的Remote Read，远程存储的Remote Read接口具体要求参照下面的请求结构，该接口的具体实现逻辑为：
1. 根据请求参数中的实例ID在monitoring命名空间下的名为vmagent-cluster-config的configmap中查找remoteReadUrl字段，获取远程存储的Remote Read接口地址, 具体的configmap格式如下，其中remote_write的url中的cprom.bj.baidubce.com为endpoint，所以remoteReadUrl为https://cprom.bj.baidubce.com/select, InstanceId为cprom-xm1mwl5c3k3s7，bearerToken为Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14bTFtd2w1YzNrM3M3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjoxNzkyMzIwMTQyLCJpc3MiOiJjcHJvbSJ9.9OC-sA9xNuvD90oCak8ltyme4Rf4Wkidu4cJA2COD38
```
apiVersion: v1
kind: ConfigMap
metadata:
  name: vmagent-cluster-config
  namespace: monitoring
data:
  prometheus.yaml: |-
    global:
      scrape_interval: 30s
    scrape_configs:
      - job_name: "stats-prometheus"
        metrics_path: /stats/prometheus
        kubernetes_sd_configs:
        - role: pod
        metric_relabel_configs:
          - source_labels: [destination_address]
            regex: '([^:]+):\d+'
            target_label: 'destination_address'
            replacement: '$1'
            action: replace
          - source_labels: [__name_] 
            regex: '(go_.+|process_.+|scrape_.+|__meta_.+)'
            action: drop
        relabel_configs:
          # 通用标签：添加命名空间信息（后续用于 remote_write 过滤）
          - source_labels: [__meta_kubernetes_namespace]
            target_label: user_namespace
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            action: keep
            regex: '.*-envoy-prom'
    remote_write:
      - url: "https://cprom.bj.baidubce.com/insert/prometheus/api/v1/write"
        bearer_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2UiOiJjcHJvbS14bTFtd2w1YzNrM3M3Iiwic2VjcmV0TmFtZSI6ImVjYTk3ZTE0OGNiNzRlOTY4M2Q3YjcyNDA4MjlkMWZmIiwiZXhwIjoxNzkyMzIwMTQyLCJpc3MiOiJjcHJvbSJ9.9OC-sA9xNuvD90oCak8ltyme4Rf4Wkidu4cJA2COD38"
        headers:
          InstanceId: "cprom-xm1mwl5c3k3s7"
        tls_config:
          insecure_skip_verify: true
        write_relabel_configs:
          - action: replace
            target_label: "region"
            replacement: "bj"                 
          - source_labels: [user_namespace]
            action: keep
            regex: istio-system-aigw-w5d564my
```
2. 根据得到的remoteReadUrl、Authorization、InstanceId以及传入的请求参数调用远程存储的Remote Read接口，获取监控数据
3. 返回Prometheus的query_range查询结果

请求结构
使用Prometheus的query_range查询举例:
```
POST {remoteReadUrl}/prometheus/api/v1/query_range HTTP
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2Ui****HJvbS03bjU4NjN3MjEiLCJzZWNyZXROYW1lIjoiYWdlbnQtN2ZzNTNrN2MxIiwiZXhwIjoxNzE4MzM1MTA3LCJpc3MiOiJjcHJvbSJ9.5JJX_gl76aLagz8jpq7PT5AI34jdlC36IwCaYr46qWo
InstanceId: cprom-****
Content-Type: application/x-www-form-urlencoded
--data-urlencode 'query=test'
--data-urlencode 'step=60'
--data-urlencode 'start=1748328897'
--data-urlencode 'end=1748329497'
```

请求头域
除公共头域外，特殊头域：
|头域|是否必须|说明|
|---|-------|----|
|Content-Type|  是| 请求参数类型，填写 application/x-www-form-urlencoded|
|Authorization |是| 监控实例Token，获取方式参考上面的接口描述|
|InstanceId|  是| 监控实例ID|

请求参数
|参数名称|类型|是否必选|参数位置|描述|
|-----|-----|-------|-------|----|
|remoteReadUrl| String | 是| path参数 | 监控实例Remote Read的公网URL, 参考接口描述的示例图|
|query |String | 是| URL参数| 查询的指标名|
|step | Int| 是| URL参数 |查询数据步长，单位:秒|
|start| Long Int | 是| URL参数 |查询数据的开始时间戳，单位:秒|
|end| Long Int | 是| URL参数 |查询数据的结束时间戳，单位:秒|

返回头域
除公共头域外，无其它特殊头域。

返回参数
|参数名称|类型|描述|
|-----|-----|-------|
|status | String | 请求是否成功|
|data | Object | 返回的数据信息, Prometheus 的相关解释参考 Range queries|
|isPartial| Bool | 查询返回是否为部分数据|
|resultType | String | 查询到的监控数据类型|
|result | List | 查询到数据结果，包含metric 和values 信息|
|metric | Map |查询指标的所有维度|
|values | List | 查询数据的值，分别为时间戳和具体数值|

请求示例
```
POST http://cprom.gz.baidubce.com/select/prometheus/api/v1/query_range HTTP
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2Ui****HJvbS03bjU4NjN3MjEiLCJzZWNyZXROYW1lIjoiYWdlbnQtN2ZzNTNrN2MxIiwiZXhwIjoxNzE4MzM1MTA3LCJpc3MiOiJjcHJvbSJ9.5JJX_gl76aLagz8jpq7PT5AI34jdlC36IwCaYr46qWo
Content-Type: application/x-www-form-urlencoded
InstanceId: cprom-****
--data-urlencode 'query=test'
--data-urlencode 'step=60'
--data-urlencode 'start=1748328897'
--data-urlencode 'end=1748329497'
```
返回示例
```
HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8
Date: Wed, 27 May 2025 03:28:11 GMT
X-Server-Hostname: vmselect-cprom-****-1
{
  "status": "success",
  "isPartial": false,
  "data": {
     "resultType": "matrix",
     "result": [
        {
           "metric": {
              "__name__": "test",
              "env": "test"
           },
           "values": [
              [
                 1748328897,
                 "10"
              ]
           ]
        }
     ]
  }
}
```

遵守原则：尽量不要使用硬编码，循序后端开发规范
* 尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉。关键功能点前后输出日志信息，方便调试
* 最后形成接口更新总结放入 接口实现总结/监控指标目录下