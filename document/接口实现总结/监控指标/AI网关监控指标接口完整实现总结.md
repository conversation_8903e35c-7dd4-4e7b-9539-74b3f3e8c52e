# AI网关业务指标监控接口完整实现总结

## 概述

本文档总结了 AI 网关业务指标监控相关接口的完整实现，包括开启、关闭和状态检查功能。

## 实现位置

- **核心实现文件**: `cmd/csm/app/core/aigateway.go`
- **路由配置文件**: `cmd/csm/app/router/aigateway.go`
- **常量定义文件**: `pkg/util/constants/constants.go`
- **模板文件**: `templates/monitor/remote_write_entry.tmpl`

## 接口列表

### 1. EnableMetrics - 开启业务指标监控
- **路由**: `POST /v1/aigateway/:instanceId/metrics/:cpromInstanceId/enable`
- **功能**: 为 AI 网关实例开启业务指标监控功能
- **特性**: 支持状态预检查，避免重复配置

### 2. DisableMetrics - 关闭业务指标监控  
- **路由**: `POST /v1/aigateway/:instanceId/metrics/disable`
- **功能**: 关闭 AI 网关实例的业务指标监控功能
- **特性**: 支持状态检查，安全删除配置

### 3. CheckMetricsStatus - 检查监控状态
- **路由**: `GET /v1/aigateway/:instanceId/metrics/status`
- **功能**: 检测用户是否开启了业务指标监控
- **特性**: 返回详细的状态信息

### 4. QueryPrometheusMetrics - 查询Prometheus指标
- **路由**: `GET /v1/aigateway/:instanceId/metrics/query_range`
- **功能**: 查询AI网关实例的Prometheus指标数据
- **特性**: 支持基础指标和业务指标查询，使用PromQL语法

## 核心数据结构

### MetricsStatus 结构体
```go
type MetricsStatus struct {
    Enabled         bool   `json:"enabled"`                   // 是否开启监控
    CpromInstanceId string `json:"cpromInstanceId,omitempty"` // CProm实例ID（仅在开启时返回）
}
```

### PrometheusQueryRequest 结构体
```go
type PrometheusQueryRequest struct {
    InstanceId string `json:"instanceId" validate:"required"` // AI网关实例ID
    MetricType string `json:"metricType" validate:"required"` // 指标类型：basic/business
    Query      string `json:"query" validate:"required"`      // PromQL查询语句
    Step       string `json:"step" validate:"required"`       // 查询步长
    Start      string `json:"start" validate:"required"`      // 开始时间
    End        string `json:"end" validate:"required"`        // 结束时间
}
```

### QueryPrometheusMetrics 响应格式
该接口直接返回 Prometheus 原始查询结果，不进行额外包装：

```json
{
  "data": {
    "result": [...],
    "resultType": "matrix"
  },
  "isPartial": false,
  "stats": {
    "seriesFetched": "1"
  },
  "status": "success"
}
```

## 方法实现详解

### 1. EnableMetrics 方法

**核心流程**:
1. **参数验证**: 验证 instanceId 和 cpromInstanceId
2. **Token 管理**: 获取或创建 CProm 实例的访问 token
3. **实例信息获取**: 获取 AI 网关实例的 region、namespace、hostedClusterId
4. **状态预检查**: 检查是否已经开启监控，避免重复配置
5. **配置 remote_write**: 添加新的远程写入配置到 ConfigMap
6. **重启 Pod**: 重启 StatefulSet 使配置生效

**关键代码片段**:
```go
// 状态预检查
status, err := core.getMetricsStatus(ctx, region, hostedClusterId, userNamespace)
if status.Enabled {
    return ctx.JSON(http.StatusOK, map[string]interface{}{
        "success": true,
        "message": "业务指标监控已开启，无需重复配置",
        "token":   cpromAccessToken,
        "enabled": true,
    })
}
```

### 2. DisableMetrics 方法

**核心流程**:
1. **参数验证**: 验证 instanceId
2. **实例信息获取**: 获取 AI 网关实例信息
3. **状态检查**: 检查是否已经开启监控
4. **配置删除**: 删除 ConfigMap 中对应的 remote_write 配置
5. **重启 Pod**: 重启 StatefulSet 使配置生效

**关键代码片段**:
```go
// 状态检查
status, err := core.getMetricsStatus(ctx, region, hostedClusterId, userNamespace)
if !status.Enabled {
    return ctx.JSON(http.StatusOK, map[string]interface{}{
        "success": true,
        "message": "业务指标监控未开启，无需关闭",
        "enabled": false,
    })
}
```

### 3. CheckMetricsStatus 方法

**核心流程**:
1. **参数验证**: 验证 instanceId
2. **实例信息获取**: 获取 AI 网关实例信息
3. **状态检查**: 调用 getMetricsStatus 获取监控状态
4. **返回结果**: 返回详细的监控状态信息

## 辅助方法详解

### 1. getMetricsStatus
**功能**: 获取监控状态的统一方法

**核心逻辑**:
1. 创建 k8s 客户端
2. 获取 ConfigMap
3. 解析 prometheus.yaml 配置
4. 检查是否存在用户命名空间的配置
5. 返回结构化的状态信息

### 2. configurePrometheusRemoteWrite
**功能**: 配置 Prometheus Agent 的 remote_write

**核心逻辑**:
1. 获取现有 ConfigMap
2. 检查配置是否已存在
3. 渲染 remote_write 模板
4. 添加新配置到 YAML
5. 更新 ConfigMap

### 3. removePrometheusRemoteWrite
**功能**: 删除 Prometheus Agent 的 remote_write 配置

**核心逻辑**:
1. 获取现有 ConfigMap
2. 检查配置是否存在
3. 删除指定用户命名空间的配置
4. 更新 ConfigMap

### 4. isRemoteWriteConfigExists
**功能**: 检查指定用户命名空间的 remote_write 配置是否已存在

**检测机制**:
1. **精确检查**: 查找 `regex: userNamespace` 模式
2. **兼容性检查**: 验证配置是否在 `remote_write` 部分
3. **详细日志**: 提供存在性判断和日志记录

### 5. removeRemoteWriteConfig
**功能**: 从 prometheus.yaml 中删除指定用户命名空间的 remote_write 配置

**删除策略**:
1. 解析 YAML 行结构
2. 定位目标 remote_write 条目
3. 删除整个条目（包括所有子配置）
4. 重新组装 YAML 内容

### 6. isTargetRemoteWriteEntry
**功能**: 检查从指定位置开始的 remote_write 条目是否包含目标用户命名空间

**匹配逻辑**:
1. 从指定位置开始扫描
2. 检查 regex 配置中的用户命名空间
3. 确保在正确的配置范围内

### 7. extractCpromInstanceId
**功能**: 从prometheus.yaml配置中提取指定用户命名空间对应的CProm实例ID

**提取逻辑**:
1. 解析YAML行结构，定位remote_write部分
2. 找到目标用户命名空间的remote_write条目
3. 在该条目中查找InstanceId配置行
4. 提取并返回CProm实例ID

### 8. restartPrometheusAgent
**功能**: 重启 Prometheus Agent StatefulSet

**重启机制**:
1. 获取 StatefulSet
2. 更新 `kubectl.kubernetes.io/restartedAt` annotation
3. 触发 Pod 重启

## 常量定义

在 `pkg/util/constants/constants.go` 中定义的监控相关常量：

```go
// 监控相关常量
const (
    // 监控模板文件路径
    MonitorRemoteWriteTemplatePath = "templates/monitor/remote_write_entry.tmpl"
    
    // Prometheus Agent ConfigMap 相关
    PrometheusAgentConfigMapName      = "vmagent-cluster-config"
    PrometheusAgentConfigMapNamespace = "monitoring"
    PrometheusAgentConfigMapDataKey   = "prometheus.yaml"
    
    // Prometheus Agent StatefulSet 相关
    PrometheusAgentStatefulSetName      = "cluster-prometheus-agent"
    PrometheusAgentStatefulSetNamespace = "monitoring"
    
    // 重启注解
    KubernetesRestartAnnotation = "kubectl.kubernetes.io/restartedAt"
)
```

## Remote Write 模板

`templates/monitor/remote_write_entry.tmpl` 文件内容：

```yaml
- url: "https://cprom.{{.Region}}.baidubce.com/insert/prometheus/api/v1/write"
  bearer_token: "{{.BearerToken}}"
  headers:
    InstanceId: "{{.InstanceId}}"
  tls_config:
    insecure_skip_verify: true
  metadata_config:
    send: false  # 关闭元数据推送
    send_interval: 0s  # 禁用周期性元数据同步
  write_relabel_configs:
    - action: replace
      target_label: "region"
      replacement: "{{.Region}}"
    - source_labels: [user_namespace]
      action: keep
      regex: {{.UserNamespace}}
```

## 使用示例

### 1. 检查监控状态

**请求**:
```bash
GET /v1/aigateway/{instanceId}/metrics/status
```

**响应示例**:

**监控已开启时**:
```json
{
  "enabled": true,
  "cpromInstanceId": "cprom-x35s7xaq7ezx7"
}
```

**监控未开启时**:
```json
{
  "enabled": false
}
```

### 2. 开启业务指标监控

**请求**:
```bash
POST /v1/aigateway/{instanceId}/metrics/{cpromInstanceId}/enable
```

**成功开启响应**:
```json
{
  "success": true,
  "message": "业务指标监控已成功开启",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "enabled": true
}
```

**已开启状态响应**:
```json
{
  "success": true,
  "message": "业务指标监控已开启，无需重复配置",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "enabled": true
}
```

### 3. 关闭业务指标监控

**请求**:
```bash
POST /v1/aigateway/{instanceId}/metrics/disable
```

**成功关闭响应**:
```json
{
  "success": true,
  "message": "业务指标监控已成功关闭",
  "enabled": false
}
```

**未开启状态响应**:
```json
{
  "success": true,
  "message": "业务指标监控未开启，无需关闭",
  "enabled": false
}
```

## 技术特点

### 1. 幂等性保证
- **EnableMetrics**: 开启前检查状态，已开启时返回友好提示
- **DisableMetrics**: 关闭前检查状态，未开启时返回友好提示
- **CheckMetricsStatus**: 纯查询操作，天然幂等

### 2. 精确配置管理
- **精确匹配**: 基于用户命名空间的 regex 配置进行精确匹配
- **安全删除**: 只删除目标用户的配置，不影响其他用户
- **配置验证**: 多层验证确保配置的正确性

### 3. 完善的错误处理
- **参数验证**: 严格的参数验证机制
- **资源检查**: 检查必要资源的存在性
- **异常处理**: 完善的异常捕获和处理
- **日志记录**: 详细的操作日志，便于问题排查

### 4. 模板化配置
- **模板文件**: 使用 `.tmpl` 文件进行配置模板化
- **动态渲染**: 支持动态参数替换
- **路径管理**: 通过常量统一管理模板路径

### 5. Kubernetes 集成
- **ConfigMap 操作**: 安全的 ConfigMap 读写操作
- **StatefulSet 管理**: 通过 annotation 触发 Pod 重启
- **命名空间隔离**: 支持多命名空间的配置管理

## 工作流程

### 开启监控完整流程
```
1. 用户调用 EnableMetrics 接口
2. 验证参数（instanceId, cpromInstanceId）
3. 获取或创建 CProm token
4. 获取 AI 网关实例信息
5. 检查当前监控状态
6. 如果未开启：
   - 渲染 remote_write 模板
   - 更新 ConfigMap 配置
   - 重启 Prometheus Agent
7. 返回操作结果
```

### 关闭监控完整流程
```
1. 用户调用 DisableMetrics 接口
2. 验证参数（instanceId）
3. 获取 AI 网关实例信息
4. 检查当前监控状态
5. 如果已开启：
   - 删除对应的 remote_write 配置
   - 更新 ConfigMap
   - 重启 Prometheus Agent
6. 返回操作结果
```

### 状态检查流程
```
1. 用户调用 CheckMetricsStatus 接口
2. 验证参数（instanceId）
3. 获取 AI 网关实例信息
4. 获取 ConfigMap 配置
5. 解析并检查 remote_write 配置
6. 返回详细状态信息
```

## 注意事项

### 1. 权限要求
- 需要对托管集群的 `monitoring` 命名空间有读写权限
- 需要对 ConfigMap 和 StatefulSet 资源的操作权限

### 2. 配置生效时间
- Pod 重启后配置才会生效，可能需要等待一段时间
- 建议在操作后等待 30-60 秒再进行验证

### 3. 资源依赖
- 依赖 `vmagent-cluster-config` ConfigMap 的存在
- 依赖 `cluster-prometheus-agent` StatefulSet 的存在
- 依赖托管集群的正常运行

### 4. 并发安全
- 接口支持并发调用
- ConfigMap 更新使用乐观锁机制
- 建议避免同一实例的并发操作

## 监控和运维

### 1. 日志监控
- 所有操作都有详细的日志记录
- 关键操作点都有 INFO 级别日志
- 错误情况有 ERROR 级别日志

### 2. 状态验证
- 可通过 CheckMetricsStatus 接口验证配置状态
- 可通过 Prometheus 查询验证数据采集状态
- 可通过 Kubernetes 命令检查 Pod 状态

### 3. 故障排查
- 检查 ConfigMap 配置是否正确
- 检查 StatefulSet 是否正常重启
- 检查网络连通性和权限配置
- 查看详细的操作日志

## 总结

AI 网关业务指标监控接口体系提供了完整、安全、易用的监控管理功能：

### 核心优势
- **完整性**: 提供开启、关闭、状态检查的完整功能闭环
- **安全性**: 精确的配置识别和操作，不会影响其他用户
- **可靠性**: 完善的错误处理和幂等性保证
- **易用性**: 友好的接口设计和响应格式
- **可维护性**: 常量化配置管理和模板化设计

### 技术亮点
- 精确的 YAML 配置解析和修改
- 安全的 Kubernetes 资源操作
- 完善的状态检查和验证机制
- 模板化的配置管理
- 详细的日志记录和错误处理

该实现完全符合项目规范，具备生产环境的稳定性和可靠性，为 AI 网关用户提供了专业级的业务指标监控管理功能。
