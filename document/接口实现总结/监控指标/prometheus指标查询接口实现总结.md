# Prometheus指标查询接口实现总结

## 接口概述

**接口路径**: `GET /v1/aigateway/:instanceId/metrics/query_range`

**功能描述**: 为AI网关实例提供Prometheus指标查询功能，支持时间范围内的指标数据查询

## 重构历程

### 第一阶段重构：接口统一化
- **问题**: 原有设计需要调用3个独立接口，调用复杂
- **解决方案**: 将3个接口合并为1个统一入口
- **改进效果**: Core层代码从30多行减少到5行

### 第二阶段重构：数据源升级  
- **问题**: 原使用旧的instance表查询实例信息
- **解决方案**: 升级为新的AI网关专用数据库
- **改进效果**: 数据一致性提高，托管集群信息直接从数据库获取

## 最新实现架构

### 核心组件

1. **统一接口**: `QueryPrometheusMetrics`
2. **数据源**: AI网关数据库 (`t_ai_gateway_instance`表)
3. **配置获取**: 内部方法 `getPrometheusRemoteConfig`
4. **远程调用**: 内部方法 `callPrometheusRemoteRead`

### 数据流程

```mermaid
graph TD
    A[Core Layer] --> B[MonitorService.QueryPrometheusMetrics]
    B --> C[1. 查询AI网关信息<br/>AIGatewayModel.GetAIGatewayInfo]
    B --> D[2. 提取关键信息<br/>region, namespace, hostedClusterId]
    B --> E[3. 获取Prometheus配置<br/>getPrometheusRemoteConfig]
    B --> F[4. 调用远程Prometheus<br/>callPrometheusRemoteRead]
    
    C --> G[AI网关数据库<br/>t_ai_gateway_instance]
    E --> H[解析ConfigMap<br/>vmagent-cluster-config]
    E --> I[匹配namespace正则<br/>获取remote_write配置]
    E --> J[转换为remote_read URL]
    
    F --> K[构建HTTP请求]
    F --> L[设置Authorization头]
    F --> M[调用/prometheus/api/v1/query_range]
    F --> N[返回JSON结果]
```

## 关键技术实现

### 1. 数据库查询优化

**旧实现**:
```go
// 从旧的instance表查询
csmInstance, err := service.InstanceModel.GetInstanceByInstanceUUID(ctx, instanceId)
region := csmInstance.Region
userNamespace := csmInstance.IstioInstallNamespace

// 从配置文件获取托管集群
hostedClusterId, _ := service.opt.GetHostingCluster(region)
```

**新实现**:
```go
// 从新的AI网关数据库查询
gatewayInfo, err := service.AIGatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
gateway := **gatewayInfo

// 直接从数据库获取完整信息
region := gateway.Region
userNamespace := gateway.Namespace
hostedClusterId := gateway.HostedClusterID
hostedClusterName := gateway.HostedClusterName
```

### 2. 字段对应关系

| 用途 | 旧实现 | 新实现 |
|------|--------|--------|
| 区域信息 | `csmInstance.Region` | `gateway.Region` |
| 命名空间 | `csmInstance.IstioInstallNamespace` | `gateway.Namespace` |
| 托管集群ID | `service.opt.GetHostingCluster(region)` | `gateway.HostedClusterID` |
| 托管集群名称 | `service.opt.GetHostingCluster(region)` | `gateway.HostedClusterName` |

### 3. 依赖关系优化

**旧实现依赖**:
- instances service (产生循环依赖)
- 配置文件 (cloud.hostingRegion)
- 多个model层调用

**新实现依赖**:
- aigateway model (专用数据库)
- 减少配置依赖
- 统一数据源

## Service层结构

### 服务结构体
```go
type Service struct {
    opt             *Option
    cceService      cce.ClientInterface
    MonitorModel    monitorModel.ServiceInterface
    InstanceModel   instances.ServiceInterface
    ClusterModel    cluster.ServiceInterface
    AIGatewayModel  aigateway.ServiceInterface  // 新增
}
```

### 接口定义
```go
type ServiceInterface interface {
    // 其他方法...
    
    // prometheus查询接口 - 统一入口
    QueryPrometheusMetrics(ctx context.CsmContext, instanceId, query, step, start, end string) (interface{}, error)
}
```

## 核心方法实现

### QueryPrometheusMetrics方法
```go
func (service *Service) QueryPrometheusMetrics(ctx csmContext.CsmContext, instanceId, query, step, start, end string) (interface{}, error) {
    // 1. 从新数据库中查询AI网关实例的信息
    gatewayInfo, err := service.AIGatewayModel.GetAIGatewayInfo(ctx, instanceId, instanceId)
    if err != nil {
        return nil, csmErr.NewServiceException("failed to get AI gateway info", err)
    }
    
    // 2. 提取网关信息
    gateway := **gatewayInfo
    region := gateway.Region
    userNamespace := gateway.Namespace
    hostedClusterId := gateway.HostedClusterID
    
    // 3. 验证托管集群信息
    if len(hostedClusterId) == 0 {
        return nil, csmErr.NewInvalidParameterValueException("no hosting cluster found")
    }
    
    // 4. 获取prometheus远程存储配置
    remoteConfig, err := service.getPrometheusRemoteConfig(ctx, region, userNamespace, hostedClusterId)
    if err != nil {
        return nil, err
    }
    
    // 5. 调用远程存储的query_range接口
    result, err := service.callPrometheusRemoteRead(ctx, remoteConfig, query, step, start, end)
    if err != nil {
        return nil, err
    }
    
    return result, nil
}
```

## 使用示例

### Core层调用
```go
// 简化的调用方式
result, err := core.MonitorService.QueryPrometheusMetrics(ctx, instanceId, query, step, start, end)
if err != nil {
    return err
}
return ctx.JSON(http.StatusOK, result)
```

### HTTP请求示例
```bash
curl -X GET "http://localhost:8080/v1/aigateway/test-instance-id/metrics/query_range?query=up&step=60s&start=1640995200&end=1640995800"
```

## 重构优势

### 1. 简洁性
- **调用简化**: 从3次service调用减少到1次
- **代码减少**: Core层代码量减少85%
- **接口统一**: 单一入口，职责明确

### 2. 可维护性
- **数据一致性**: 直接从AI网关数据库获取准确信息
- **减少配置依赖**: 无需维护复杂的配置文件映射
- **逻辑集中**: 所有相关逻辑封装在service内部

### 3. 扩展性
- **数据源独立**: AI网关有专用数据表，便于扩展
- **托管集群信息完整**: 包含ID和名称，便于后续功能扩展
- **错误处理完善**: 详细的错误信息，便于问题排查

### 4. 性能优化
- **减少数据库查询**: 一次查询获取所有必要信息
- **避免循环依赖**: 解决了instances service的循环导入问题
- **缓存友好**: 统一的数据结构便于实现缓存机制

## 总结

通过两阶段重构，Prometheus指标查询接口已经完全适配AI网关的新架构：

1. **第一阶段**: 实现了接口统一化，解决了调用复杂的问题
2. **第二阶段**: 升级数据源到AI网关专用数据库，提高了数据一致性和可维护性

新实现具有更好的可维护性、扩展性和性能，为AI网关的监控功能提供了坚实的技术基础。