# 创建路由接口

## 接口基本信息

- **接口说明**：创建一个新的API路由
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/{clusterId}/route`
- **请求方式**：POST
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| clusterId | String | 是 | 集群ID | cce-qhjz40ds |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 请求体

```json
{
    "routeName": "inference-api",
    "matchRules": {
        "pathRule": {
            "matchType": "prefix | exact",
            "value": "/v1/models/",
            "caseSensitive": true
        },
        "methods": ["GET", "POST"],
        "headers": [
            {
                "key": "Content-Type",
                "matchType": "exact",
                "value": "application/json"
            }
        ],
        "queryParams": [
            {
                "key": "version",
                "matchType": "exact",
                "value": "v1"
            }
        ]
    },
    "multiService": false,
    "targetService": {
        "serviceSource": "CCE",
        "serviceName": "mnist-inference",
        "namespace": "default",
        "servicePort": 8080,
        "loadBalanceAlgorithm": "consistent-hash",
        "hashType": "header",
        "hashKey": "X-Request-Id"
    },
    "timeoutPolicy": {
        "enabled": true,
        "timeout": 5
    },
    "retryPolicy": {
        "enabled": true,
        "retryConditions": "gateway-error,connect-failure,refused-stream,5xx",
        "numRetries": 2
    },
    "rewrite": {
        "enabled": true,
        "path": "/api/v1/inference"
    },
    "authEnabled": false,
    "allowedConsumers": ["consumer-1", "consumer-2"],
    "tokenRateLimit": {
        "enabled": true,
        "rule_items": [
            {
                "match_condition": {
                    "type": "consumer",
                    "key": "",
                    "value": "consumer_name"
                },
                "limit_config": {
                    "time_unit": "minute",
                    "token_amount": 1000
                }
            }
        ]
    }
}
```

#### 多服务模式示例（按比例分发）

```json
{
    "routeName": "inference-api-multi",
    "matchRules": {
        "pathRule": {
            "matchType": "prefix",
            "value": "/v1/models/",
            "caseSensitive": true
        },
        "methods": ["GET", "POST"]
    },
    "multiService": true,
    "trafficDistributionStrategy": "ratio",
    "targetService": [
        {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference-1",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "consistent-hash",
            "hashType": "header",
            "hashKey": "X-Request-Id",
            "requestRatio": 70
        },
        {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference-2",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "round-robin",
            "requestRatio": 30
        }
    ],
    "rewrite": {
        "enabled": false
    },
    "authEnabled": false,
    "tokenRateLimit": {
        "enabled": false
    },
    "timeoutPolicy": {
      "enabled": true,
      "timeout": 5
    },
    "retryPolicy": {
      "enabled": true,
      "retryConditions": "gateway-error,connect-failure,refused-stream,5xx",
      "numRetries": 2
    }
}
```

#### 多服务模式示例（按模型名称分发）

```json
{
    "routeName": "inference-api-model",
    "matchRules": {
        "pathRule": {
            "matchType": "prefix",
            "value": "/v1/models/",
            "caseSensitive": true
        },
        "methods": ["GET", "POST"]
    },
    "multiService": true,
    "trafficDistributionStrategy": "model_name",
    "targetService": [
        {
            "serviceSource": "CCE",
            "serviceName": "gpt-4-service",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "consistent-hash",
            "hashType": "header",
            "hashKey": "X-Request-Id",
            "modelName": "gpt-4"
        },
        {
            "serviceSource": "CCE",
            "serviceName": "gpt-3-service",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "round-robin",
            "modelName": "gpt-3.5-turbo"
        }
    ],
    "rewrite": {
        "enabled": false
    },
    "authEnabled": false,
    "tokenRateLimit": {
        "enabled": false
    },
    "timeoutPolicy": {
      "enabled": true,
      "timeout": 5
    },
    "retryPolicy": {
      "enabled": true, 
      "retryConditions": "gateway-error,connect-failure,refused-stream,5xx", 
      "numRetries": 2
    }  
}
```

### 请求体字段说明

| 字段名                                | 类型 | 是否必填 | 描述        | 示例值 | 限制                                              |
|------------------------------------| --- | --- |-----------| --- |-------------------------------------------------|
| routeName                          | String | 是 | 路由名称      | inference-api | 长度为2-64个字符，可包含字母、数字、下划线(_)和连字符(-)               |
| matchRules                         | Object | 是 | 匹配规则      | - | 包含路径、方法、请求头、请求参数等匹配规则                           |
| matchRules.pathRule                | Object | 是 | 路径参数规则    | - | 路径匹配规则                                          |
| matchRules.pathRule.matchType      | String | 是 | 匹配方式      | prefix | 可选值：prefix(前缀匹配)、exact(精确匹配)       |
| matchRules.pathRule.value          | String | 是 | 路径匹配值     | /v1/models/ | 匹配的URL路径                                        |
| matchRules.pathRule.caseSensitive  | Boolean | 否 | 是否大小写敏感   | true | true: 区分大小写, false: 不区分大小写，默认为true              |
| matchRules.methods                 | Array[String] | 是 | 请求方法      | ["GET", "POST"] | HTTP方法，如GET、POST、PUT、DELETE等，支持多选               |
| matchRules.headers                 | Array[Object] | 否 | 请求头规则     | - | 请求头匹配规则，支持多个                                    |
| matchRules.headers[].key           | String | 是 | 请求头名称     | Content-Type | HTTP请求头名称                                       |
| matchRules.headers[].matchType     | String | 是 | 匹配规则      | exact | 可选值：prefix(前缀匹配)、exact(精确匹配)                    |
| matchRules.headers[].value         | String | 是 | 请求头值      | application/json | 请求头值                                            |
| matchRules.queryParams             | Array[Object] | 否 | 请求参数规则    | - | 请求参数匹配规则，支持多个                                   |
| matchRules.queryParams[].key       | String | 是 | 参数名       | version | 查询参数名                                           |
| matchRules.queryParams[].matchType | String | 是 | 匹配规则      | exact | 可选值：prefix(前缀匹配)、exact(精确匹配)                    |
| matchRules.queryParams[].value     | String | 是 | 参数值       | v1 | 查询参数值                                           |
| multiService                       | Boolean | 否 | 是否开启多服务   | false | true: 开启多服务, false: 单服务，默认为false                |
| trafficDistributionStrategy        | String | 条件必填 | 流量分发策略    | ratio | 当multiService=true时必填，可选值：ratio(按比例)、model_name(按模型名称) |
| targetService                      | Object/Array | 是 | 目标服务      | - | 单服务时为Object，多服务时为Array                          |
| targetService.serviceSource        | String | 是 | 服务来源      | CCE | 目前仅支持"CCE"(容器引擎)                                |
| targetService.serviceName          | String | 是 | 服务名称      | mnist-inference | 必须是已存在的服务名称                                     |
| targetService.namespace            | String | 是 | 命名空间      | default | 必须是服务所在的命名空间                                    |
| targetService.servicePort          | Integer | 是 | 服务端口      | 8080 | 取值范围：1-65535                                    |
| targetService.loadBalanceAlgorithm | String | 否 | 负载均衡算法    | consistent-hash | 可选值：round-robin、least-conn、random、consistent-hash |
| targetService.requestRatio         | Integer | 条件必填 | 请求比例      | 70 | 当trafficDistributionStrategy=ratio时必填，取值范围：1-100，所有服务的比例总和应为100 |
| targetService.modelName            | String | 条件必填 | 模型名称      | gpt-4 | 当trafficDistributionStrategy=model_name时必填 |
| targetService.hashType             | String | 条件必填 | 哈希一致性类型  | header | 当loadBalanceAlgorithm=consistent-hash时必填，可选值：header、query_param、ip、cookie |
| targetService.hashKey              | String | 条件必填 | 哈希一致性参数  | X-Request-Id | 当loadBalanceAlgorithm=consistent-hash时必填，对应的header名、参数名、cookie名等 |
| rewrite                            | Object | 否 | 路径重写配置    | - | 路径重写相关配置                                        |
| rewrite.enabled                    | Boolean | 是 | 是否启用路径重写  | true | true: 启用路径重写, false: 不重写路径，默认为false             |
| rewrite.path                       | String | 条件必填 | 重写后的路径    | /api/v1/inference | 当enabled=true时必填，重写后转发到目标服务的路径                 |
| authEnabled                        | Boolean | 否 | 是否开启消费者认证 | true | true: 开启, false: 关闭，默认为false                    |
| allowedConsumers                   | Array[String] | 否 | 可访问的消费者列表 | ["consumer-1"] | 允许访问的消费者ID列表，仅当authEnabled=true时有效              |
| tokenRateLimit                     | Object | 否 | Token限流配置 | - | Token限流相关配置 |
| tokenRateLimit.enabled             | Boolean | 是 | 是否开启Token限流 | true | true: 开启, false: 关闭 |
| tokenRateLimit.rule_items          | Array[Object] | 条件必填 | 限流策略列表 | - | 当enabled=true时必填，支持添加多个限流策略 |
| tokenRateLimit.rule_items[].match_condition | Object | 是 | 限流规则 | - | 匹配条件 |
| tokenRateLimit.rule_items[].match_condition.type | String | 是 | 限流类型 | consumer | 可选值：consumer(按消费者)、header(按请求头)、query_param(按请求参数) |
| tokenRateLimit.rule_items[].match_condition.key | String | 是 | 限流键名 | consumer_name | 根据type传对应的值：消费者类型时为空字符串、请求头名、请求参数名 |
| tokenRateLimit.rule_items[].match_condition.value | String | 条件必填 | 限流键值 | - | 根据type传对应的值：消费者名称、请求头值、请求参数值 |
| tokenRateLimit.rule_items[].limit_config | Object | 是 | 限流配置 | - | 限流具体配置 |
| tokenRateLimit.rule_items[].limit_config.time_unit | String | 是 | 时间单位 | minute | 可选值：second(每秒)、minute(每分钟)、hour(每小时)、day(每天) |
| tokenRateLimit.rule_items[].limit_config.token_amount | Integer | 是 | Token总数 | 1000 | 必须大于0的整数 |
| timeoutPolicy                        | Object | 否 | 超时策略 | {"enabled":true,"timeout":5} | enabled为true时timeout必填，单位秒 |
| timeoutPolicy.enabled                | Boolean | 是 | 是否开启超时策略 | true | true: 开启，false: 关闭 |
| timeoutPolicy.timeout                | Integer | 条件必填 | 超时时长 | 5 | enabled为true时必填，单位秒，正整数 |
| retryPolicy                          | Object | 否 | 重试策略 | {"enabled":true,"retryConditions":"gateway-error,5xx","numRetries":2} | enabled为true时retryConditions和numRetries必填 |
| retryPolicy.enabled                  | Boolean | 是 | 是否开启重试策略 | true | true: 开启，false: 关闭 |
| retryPolicy.retryConditions          | String | 条件必填 | 重试条件 | gateway-error,5xx | enabled为true时必填，多个条件用逗号分隔 |
| retryPolicy.numRetries               | Integer | 条件必填 | 重试次数 | 2 | enabled为true时必填，正整数 |

## 响应参数

### 响应体结构

#### 单服务模式响应

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeId": "rt-f3b9e2d1",
        "routeName": "inference-api",
        "matchPath": "/v1/models/",
        "serviceName": "mnist-inference",
        "createTime": "2025-04-18 15:20:35",
        "multiService": false,
        "targetService": {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "consistent-hash",
            "hashType": "header",
            "hashKey": "X-Request-Id"
        },
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": true,
            "rule_items": [
                {
                    "match_condition": {
                        "type": "consumer",
                        "key": "",
                        "value": "consumer_name"
                    },
                    "limit_config": {
                        "time_unit": "minute",
                        "token_amount": 1000
                    }
                }
            ]
        },
        "timeoutPolicy": {
            "enabled": true,
            "timeout": 5
        },
        "retryPolicy": {
            "enabled": true,
            "retryConditions": "gateway-error,5xx",
            "numRetries": 2
        }
    }
}
```

#### 多服务模式响应

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeId": "rt-f3b9e2d1",
        "routeName": "inference-api-multi",
        "matchPath": "/v1/models/",
        "serviceName": "multi-service",
        "createTime": "2025-04-18 15:20:35",
        "multiService": true,
        "trafficDistributionStrategy": "ratio",
        "targetService": [
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-1",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 70
            },
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-2",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 30
            }
        ],
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": false
        },
        "timeoutPolicy": {
            "enabled": true,
            "timeout": 5
        },
        "retryPolicy": {
            "enabled": true,
            "retryConditions": "gateway-error,5xx",
            "numRetries": 2
        }
    }
}
```

### 错误响应体结构

```json
{
    "success": false,
    "status": 400,
    "message": "路由名称已存在"
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 创建的路由信息 |
| result.routeId | String | 路由ID |
| result.routeName | String | 路由名称 |
| result.matchPath | String | 匹配路径 |
| result.serviceName | String | 目标服务名称，多服务时显示为"multi-service" |
| result.createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| result.multiService | Boolean | 是否开启多服务 |
| result.trafficDistributionStrategy | String | 流量分发策略，仅当multiService=true时返回 |
| result.targetService | Object/Array | 目标服务配置，单服务时为Object，多服务时为Array |
| result.targetService.serviceSource | String | 服务来源 |
| result.targetService.serviceName | String | 服务名称 |
| result.targetService.namespace | String | 命名空间 |
| result.targetService.servicePort | Integer | 服务端口 |
| result.targetService.loadBalanceAlgorithm | String | 负载均衡算法 |
| result.targetService.requestRatio | Integer | 请求比例，仅当trafficDistributionStrategy=ratio时返回 |
| result.targetService.modelName | String | 模型名称，仅当trafficDistributionStrategy=model_name时返回 |
| result.targetService.hashType | String | 哈希一致性类型 |
| result.targetService.hashKey | String | 哈希一致性参数 |
| result.tokenRateLimit | Object | Token限流配置 |
| result.tokenRateLimit.rule_name | String | 限流规则名称，由后端自动生成 |
| result.tokenRateLimit.enabled | Boolean | 是否开启Token限流 |
| result.tokenRateLimit.rule_items | Array[Object] | 限流策略列表 |
| result.tokenRateLimit.rule_items[].match_condition | Object | 限流规则 |
| result.tokenRateLimit.rule_items[].match_condition.type | String | 限流类型：consumer(按消费者)、header(按请求头)、query_param(按请求参数) |
| result.tokenRateLimit.rule_items[].match_condition.key | String | 限流键名 |
| result.tokenRateLimit.rule_items[].match_condition.value | String | 限流键值 |
| result.tokenRateLimit.rule_items[].limit_config | Object | 限流配置 |
| result.tokenRateLimit.rule_items[].limit_config.time_unit | String | 时间单位：second(每秒)、minute(每分钟)、hour(每小时)、day(每天) |
| result.tokenRateLimit.rule_items[].limit_config.token_amount | Integer | Token总数 |
| result.timeoutPolicy | Object | 超时策略 |
| result.timeoutPolicy.enabled | Boolean | 是否开启超时策略 |
| result.timeoutPolicy.timeout | Integer | 超时时长（秒） |
| result.retryPolicy | Object | 重试策略 |
| result.retryPolicy.enabled | Boolean | 是否开启重试策略 |
| result.retryPolicy.retryConditions | String | 重试条件 |
| result.retryPolicy.numRetries | Integer | 重试次数 |
| message | String | 错误信息，仅在失败时返回 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有创建路由的权限 |
| 404 | 实例、集群或服务不存在 | 确认实例ID、集群ID和服务名称是否正确 |
| 409 | 路由已存在 | 路由名称或匹配规则已存在，请更换 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

#### 单服务模式

```
POST /api/aigw/v1/aigateway/gw-ist9vvin/cce-qhjz40ds/route
X-Region: gz
Content-Type: application/json

{
    "routeName": "inference-api",
    "matchRules": {
        "pathRule": {
            "matchType": "prefix",
            "value": "/v1/models/",
            "caseSensitive": true
        },
        "methods": ["GET", "POST"],
        "headers": [
            {
                "key": "Content-Type",
                "matchType": "exact",
                "value": "application/json"
            }
        ],
        "queryParams": [
            {
                "key": "version",
                "matchType": "exact",
                "value": "v1"
            }
        ]
    },
    "multiService": false,
    "targetService": {
        "serviceSource": "CCE",
        "serviceName": "mnist-inference",
        "namespace": "default",
        "servicePort": 8080,
        "loadBalanceAlgorithm": "consistent-hash",
        "hashType": "header",
        "hashKey": "X-Request-Id"
    },
    "timeoutPolicy": {
        "enabled": true,
        "timeout": 5
    },
    "retryPolicy": {
        "enabled": true,
        "retryConditions": "gateway-error,5xx",
        "numRetries": 2
    },
    "rewrite": {
        "enabled": true,
        "path": "/api/v1/inference"
    },
    "authEnabled": true,
    "allowedConsumers": ["consumer-1", "consumer-2"],
    "tokenRateLimit": {
        "enabled": true,
        "rule_items": [
            {
                "match_condition": {
                    "type": "consumer",
                    "key": "",
                    "value": "consumer_name"
                },
                "limit_config": {
                    "time_unit": "minute",
                    "token_amount": 1000
                }
            }
        ]
    }
}
```

#### 多服务模式（按比例分发）

```
POST /api/aigw/v1/aigateway/gw-ist9vvin/cce-qhjz40ds/route
X-Region: gz
Content-Type: application/json

{
    "routeName": "inference-api-multi",
    "matchRules": {
        "pathRule": {
            "matchType": "prefix",
            "value": "/v1/models/",
            "caseSensitive": true
        },
        "methods": ["GET", "POST"]
    },
    "multiService": true,
    "trafficDistributionStrategy": "ratio",
    "targetService": [
        {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference-1",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "round-robin",
            "requestRatio": 70
        },
        {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference-2",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "round-robin",
            "requestRatio": 30
        }
    ],
    "rewrite": {
        "enabled": false
    },
    "authEnabled": false,
    "tokenRateLimit": {
        "enabled": false
    },
    "timeoutPolicy": {
        "enabled": true,
        "timeout": 5
    },
    "retryPolicy": {
        "enabled": true,
        "retryConditions": "gateway-error,5xx",
        "numRetries": 2
    }
}
```

## 响应示例

### 成功响应示例（单服务）

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeId": "rt-f3b9e2d1",
        "routeName": "inference-api",
        "matchPath": "/v1/models/",
        "serviceName": "mnist-inference",
        "createTime": "2025-04-18 15:20:35",
        "multiService": false,
        "targetService": {
            "serviceSource": "CCE",
            "serviceName": "mnist-inference",
            "namespace": "default",
            "servicePort": 8080,
            "loadBalanceAlgorithm": "consistent-hash",
            "hashType": "header",
            "hashKey": "X-Request-Id"
        },
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": true,
            "rule_items": [
                {
                    "match_condition": {
                        "type": "consumer",
                        "key": "",
                        "value": "consumer_name"
                    },
                    "limit_config": {
                        "time_unit": "minute",
                        "token_amount": 1000
                    }
                }
            ]
        },
        "timeoutPolicy": {
            "enabled": true,
            "timeout": 5
        },
        "retryPolicy": {
            "enabled": true,
            "retryConditions": "gateway-error,5xx",
            "numRetries": 2
        }
    }
}
```

### 成功响应示例（多服务）

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeId": "rt-f3b9e2d1",
        "routeName": "inference-api-multi",
        "matchPath": "/v1/models/",
        "serviceName": "multi-service",
        "createTime": "2025-04-18 15:20:35",
        "multiService": true,
        "trafficDistributionStrategy": "ratio",
        "targetService": [
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-1",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 70
            },
            {
                "serviceSource": "CCE",
                "serviceName": "mnist-inference-2",
                "namespace": "default",
                "servicePort": 8080,
                "loadBalanceAlgorithm": "round-robin",
                "requestRatio": 30
            }
        ],
        "tokenRateLimit": {
            "rule_name": "rate-limit-a1b2c3",
            "enabled": false
        },
        "timeoutPolicy": {
            "enabled": true,
            "timeout": 5
        },
        "retryPolicy": {
            "enabled": true,
            "retryConditions": "gateway-error,5xx",
            "numRetries": 2
        }
    }
}
```

### 错误响应示例

```json
{
    "success": false,
    "status": 400,
    "message": "路由名称已存在"
}
```

## 注意事项

1. 实例ID和集群ID必须符合规范格式，通常分别以特定前缀（如`gw-`和`cce-`）开头
2. 路由名称在同一实例内必须唯一
3. 路径匹配规则（pathRule）必须指定，是匹配API请求的基础条件
4. 如果指定了请求方法（methods），则只有指定的HTTP方法才会被路由匹配
5. 请求头（headers）和请求参数（queryParams）是可选的额外匹配条件，可以提高匹配精确性
6. 单服务模式下，targetService为Object；多服务模式下，targetService为Array
7. 开启多服务时，必须指定流量分发策略（trafficDistributionStrategy）
8. 按比例分发时，所有服务的requestRatio总和必须等于100
9. 按模型名称分发时，每个服务必须指定唯一的modelName
10. 服务端口（servicePort）必须是目标服务开放的有效端口
11. 如果开启了消费者认证（authEnabled=true），则只有指定的消费者（allowedConsumers）才能访问该API
12. 如果未指定允许的消费者列表，即使开启了认证，也不会有消费者能够访问
13. 如果开启了Token限流（tokenRateLimit.enabled=true），则必须提供至少一条限流策略（rule_items）
14. 如果开启了路径重写（rewrite.enabled=true），则必须提供重写后的路径（rewrite.path），请求将转发到目标服务的指定路径
15. 路径重写功能用于将前端请求路径转换为后端服务的实际路径，不影响路径匹配规则
16. 创建路由后，路由将处于已发布状态，可立即使用
17. 新增超时策略(timeoutPolicy)和重试策略(retryPolicy)字段，若开启则相关字段必填
18. 负载均衡算法支持consistent-hash，需指定hashType和hashKey 